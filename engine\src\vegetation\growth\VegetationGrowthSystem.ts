/**
 * 植被生长系统
 * 用于实现植物的动态生长系统
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { Debug } from '../../utils/Debug';
import { EventEmitter } from '../../utils/EventEmitter';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';
import { VegetationComponent } from '../components/VegetationComponent';
import { TerrainComponent } from '../../terrain/components/TerrainComponent';
import { VegetationType, VegetationGrowthStage, SeasonType } from '../ecosystem/EcosystemSimulationSystem';

/**
 * 植被生长系统配置
 */
export interface VegetationGrowthSystemConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
  /** 是否使用季节变化 */
  useSeasonalChanges?: boolean;
  /** 是否使用生长阶段 */
  useGrowthStages?: boolean;
  /** 是否使用年龄系统 */
  useAgeSystem?: boolean;
  /** 是否使用健康系统 */
  useHealthSystem?: boolean;
  /** 是否使用环境影响 */
  useEnvironmentInfluence?: boolean;
  /** 生长速度倍率 */
  growthSpeedMultiplier?: number;
}

/**
 * 植被生长系统事件类型
 */
export enum VegetationGrowthSystemEventType {
  /** 植被生长 */
  VEGETATION_GROWTH = 'vegetation_growth',
  /** 植被生长阶段变化 */
  GROWTH_STAGE_CHANGED = 'growth_stage_changed',
  /** 植被死亡 */
  VEGETATION_DEATH = 'vegetation_death',
  /** 植被繁殖 */
  VEGETATION_REPRODUCTION = 'vegetation_reproduction'
}

/**
 * 植被实例数据
 */
export interface VegetationInstanceData {
  /** 实例ID */
  id: string;
  /** 植被类型 */
  type: VegetationType;
  /** 生长阶段 */
  growthStage: VegetationGrowthStage;
  /** 年龄（天） */
  age: number;
  /** 健康度 */
  health: number;
  /** 最大寿命（天） */
  maxAge: number;
  /** 生长速度 */
  growthRate: number;
  /** 当前高度 */
  currentHeight: number;
  /** 最大高度 */
  maxHeight: number;
  /** 当前宽度 */
  currentWidth: number;
  /** 最大宽度 */
  maxWidth: number;
  /** 当前缩放 */
  currentScale: THREE.Vector3;
  /** 最大缩放 */
  maxScale: THREE.Vector3;
  /** 位置 */
  position: THREE.Vector3;
  /** 旋转 */
  rotation: THREE.Euler;
  /** 是否常绿 */
  evergreen: boolean;
  /** 是否开花 */
  flowering: boolean;
  /** 开花季节 */
  floweringSeason: SeasonType[];
  /** 是否结果 */
  fruiting: boolean;
  /** 结果季节 */
  fruitingSeason: SeasonType[];
  /** 是否落叶 */
  deciduous: boolean;
  /** 落叶季节 */
  leafFallSeason: SeasonType[];
  /** 自定义数据 */
  userData: any;
}

/**
 * 植被生长系统
 */
export class VegetationGrowthSystem extends System {
  /** 系统类型 */
  public static readonly TYPE: string = 'VegetationGrowthSystem';

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: VegetationGrowthSystemConfig = {
    enabled: true,
    autoUpdate: true,
    updateFrequency: 10,
    useDebugVisualization: false,
    useSeasonalChanges: true,
    useGrowthStages: true,
    useAgeSystem: true,
    useHealthSystem: true,
    useEnvironmentInfluence: true,
    growthSpeedMultiplier: 1.0
  };

  /** 配置 */
  private config: VegetationGrowthSystemConfig;

  /** 是否启用 */
  private enabled: boolean;

  /** 是否自动更新 */
  private autoUpdate: boolean;

  /** 更新频率 */
  private updateFrequency: number;

  /** 帧计数器 */
  private frameCount: number = 0;

  /** 是否使用调试可视化 */
  private useDebugVisualization: boolean;

  /** 是否使用季节变化 */
  private useSeasonalChanges: boolean;

  /** 是否使用生长阶段 */
  private useGrowthStages: boolean;

  /** 是否使用年龄系统 */
  private useAgeSystem: boolean;

  /** 是否使用健康系统 */
  private useHealthSystem: boolean;

  /** 是否使用环境影响 */
  private useEnvironmentInfluence: boolean;

  /** 生长速度倍率 */
  private growthSpeedMultiplier: number;

  /** 植被组件列表 */
  private vegetationComponents: Map<Entity, VegetationComponent> = new Map();

  /** 地形组件列表 */
  private terrainComponents: Map<Entity, TerrainComponent> = new Map();

  /** 植被实例数据映射 */
  private instanceDataMap: Map<string, VegetationInstanceData> = new Map();

  /** 当前季节 */
  private currentSeason: SeasonType = SeasonType.SUMMER;

  /** 季节变化计时器 */
  private seasonChangeTimer: number = 0;

  /** 季节持续时间（秒） */
  private seasonDuration: number = 300;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 性能监控器 */
  private performanceMonitor: PerformanceMonitor = new PerformanceMonitor();

  /** 调试网格列表 */
  private debugMeshes: THREE.Object3D[] = [];

  /** 当前时间 */
  private time: number = 0;

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config?: VegetationGrowthSystemConfig) {
    super(world);

    // 合并配置
    this.config = { ...VegetationGrowthSystem.DEFAULT_CONFIG, ...config };

    // 设置属性
    this.enabled = this.config.enabled !== undefined ? this.config.enabled : true;
    this.autoUpdate = this.config.autoUpdate !== undefined ? this.config.autoUpdate : true;
    this.updateFrequency = this.config.updateFrequency || 10;
    this.useDebugVisualization = this.config.useDebugVisualization !== undefined ? this.config.useDebugVisualization : false;
    this.useSeasonalChanges = this.config.useSeasonalChanges !== undefined ? this.config.useSeasonalChanges : true;
    this.useGrowthStages = this.config.useGrowthStages !== undefined ? this.config.useGrowthStages : true;
    this.useAgeSystem = this.config.useAgeSystem !== undefined ? this.config.useAgeSystem : true;
    this.useHealthSystem = this.config.useHealthSystem !== undefined ? this.config.useHealthSystem : true;
    this.useEnvironmentInfluence = this.config.useEnvironmentInfluence !== undefined ? this.config.useEnvironmentInfluence : true;
    this.growthSpeedMultiplier = this.config.growthSpeedMultiplier || 1.0;
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return VegetationGrowthSystem.TYPE;
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    super.initialize();

    // 监听实体添加和移除事件
    this.world.on('entityAdded', this.onEntityAdded.bind(this));
    this.world.on('entityRemoved', this.onEntityRemoved.bind(this));

    // 初始化现有实体
    this.world.getEntities().forEach(entity => {
      if (entity.hasComponent(VegetationComponent.TYPE)) {
        this.addVegetationComponent(entity, entity.getComponent(VegetationComponent.TYPE) as VegetationComponent);
      }
      if (entity.hasComponent(TerrainComponent.TYPE)) {
        this.addTerrainComponent(entity, entity.getComponent(TerrainComponent.TYPE) as TerrainComponent);
      }
    });

    // 初始化调试可视化
    if (this.useDebugVisualization) {
      this.initDebugVisualization();
    }

    Debug.log('VegetationGrowthSystem', '植被生长系统初始化完成');
  }

  /**
   * 销毁系统
   */
  public destroy(): void {
    // 清除调试网格
    this.clearDebugMeshes();

    // 清除植被组件
    this.vegetationComponents.clear();

    // 清除地形组件
    this.terrainComponents.clear();

    // 清除植被实例数据
    this.instanceDataMap.clear();

    super.destroy();
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.enabled || !this.autoUpdate) {
      return;
    }

    // 更新时间
    this.time += deltaTime;

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.updateFrequency !== 0) {
      return;
    }

    // 如果启用了性能监控，开始计时
    if (this.useDebugVisualization) {
      this.performanceMonitor.start('vegetationGrowthUpdate');
    }

    // 更新季节
    if (this.useSeasonalChanges) {
      this.updateSeason(deltaTime);
    }

    // 更新所有植被组件
    for (const [entity, component] of this.vegetationComponents.entries()) {
      this.updateVegetationComponent(entity, component, deltaTime);
    }

    // 如果启用了调试可视化，更新调试可视化
    if (this.useDebugVisualization) {
      this.updateDebugVisualization();
      this.performanceMonitor.end('vegetationGrowthUpdate');
    }
  }

  /**
   * 实体添加事件处理
   * @param entity 实体
   */
  private onEntityAdded(entity: Entity): void {
    if (entity.hasComponent(VegetationComponent.TYPE)) {
      this.addVegetationComponent(entity, entity.getComponent(VegetationComponent.TYPE) as VegetationComponent);
    }
    if (entity.hasComponent(TerrainComponent.TYPE)) {
      this.addTerrainComponent(entity, entity.getComponent(TerrainComponent.TYPE) as TerrainComponent);
    }
  }

  /**
   * 实体移除事件处理
   * @param entity 实体
   */
  private onEntityRemoved(entity: Entity): void {
    if (entity.hasComponent(VegetationComponent.TYPE)) {
      this.removeVegetationComponent(entity);
    }
    if (entity.hasComponent(TerrainComponent.TYPE)) {
      this.removeTerrainComponent(entity);
    }
  }

  /**
   * 添加植被组件
   * @param entity 实体
   * @param component 植被组件
   */
  public addVegetationComponent(entity: Entity, component: VegetationComponent): void {
    this.vegetationComponents.set(entity, component);

    // 初始化植被实例数据
    this.initVegetationInstanceData(entity, component);
  }

  /**
   * 移除植被组件
   * @param entity 实体
   */
  public removeVegetationComponent(entity: Entity): void {
    const component = this.vegetationComponents.get(entity);
    if (component) {
      // 移除植被实例数据
      for (const [instanceId, instance] of component.instances.entries()) {
        this.instanceDataMap.delete(instanceId);
      }
    }

    this.vegetationComponents.delete(entity);
  }

  /**
   * 添加地形组件
   * @param entity 实体
   * @param component 地形组件
   */
  public addTerrainComponent(entity: Entity, component: TerrainComponent): void {
    this.terrainComponents.set(entity, component);
  }

  /**
   * 移除地形组件
   * @param entity 实体
   */
  public removeTerrainComponent(entity: Entity): void {
    this.terrainComponents.delete(entity);
  }

  /**
   * 初始化植被实例数据
   * @param entity 实体
   * @param component 植被组件
   */
  private initVegetationInstanceData(entity: Entity, component: VegetationComponent): void {
    // 遍历所有实例
    for (const [instanceId, instance] of component.instances.entries()) {
      // 获取植被项
      const item = component.items[instance.itemIndex];

      // 创建植被实例数据
      const instanceData: VegetationInstanceData = {
        id: instanceId,
        type: this.getVegetationType(item.type),
        growthStage: VegetationGrowthStage.ADULT, // 默认为成年阶段
        age: Math.random() * 365 * 3, // 随机年龄（0-3年）
        health: 0.8 + Math.random() * 0.2, // 随机健康度（0.8-1.0）
        maxAge: this.getMaxAge(item.type),
        growthRate: 1.0 + Math.random() * 0.5, // 随机生长速度（1.0-1.5）
        currentHeight: instance.scale.y * item.height,
        maxHeight: item.height,
        currentWidth: instance.scale.x * item.width,
        maxWidth: item.width,
        currentScale: instance.scale.clone(),
        maxScale: new THREE.Vector3(1, 1, 1),
        position: instance.position.clone(),
        rotation: instance.rotation.clone(),
        evergreen: this.isEvergreen(item.type),
        flowering: false,
        floweringSeason: this.getFloweringSeason(item.type),
        fruiting: false,
        fruitingSeason: this.getFruitingSeason(item.type),
        deciduous: !this.isEvergreen(item.type),
        leafFallSeason: [SeasonType.AUTUMN],
        userData: {}
      };

      // 根据年龄更新生长阶段
      this.updateGrowthStage(instanceData);

      // 添加到实例数据映射
      this.instanceDataMap.set(instanceId, instanceData);
    }
  }

  /**
   * 更新植被组件
   * @param entity 实体
   * @param component 植被组件
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateVegetationComponent(entity: Entity, component: VegetationComponent, deltaTime: number): void {
    // 遍历所有实例
    for (const [instanceId, instance] of component.instances.entries()) {
      // 获取植被实例数据
      const instanceData = this.instanceDataMap.get(instanceId);
      if (!instanceData) {
        continue;
      }

      // 更新植被实例
      this.updateVegetationInstance(entity, component, instanceId, instance, instanceData, deltaTime);
    }
  }

  /**
   * 更新植被实例
   * @param entity 实体
   * @param component 植被组件
   * @param instanceId 实例ID
   * @param instance 实例
   * @param instanceData 实例数据
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateVegetationInstance(
    entity: Entity,
    component: VegetationComponent,
    instanceId: string,
    instance: any,
    instanceData: VegetationInstanceData,
    deltaTime: number
  ): void {
    // 如果使用年龄系统，更新年龄
    if (this.useAgeSystem) {
      this.updateAge(instanceData, deltaTime);
    }

    // 如果使用健康系统，更新健康度
    if (this.useHealthSystem) {
      this.updateHealth(instanceData, deltaTime);
    }

    // 如果使用生长阶段，更新生长阶段
    if (this.useGrowthStages) {
      this.updateGrowthStage(instanceData);
    }

    // 如果使用季节变化，更新季节性特征
    if (this.useSeasonalChanges) {
      this.updateSeasonalFeatures(instanceData, this.currentSeason);
    }

    // 更新大小
    this.updateSize(instanceData, deltaTime);

    // 更新实例
    this.updateInstanceTransform(entity, component, instanceId, instance, instanceData);
  }

  /**
   * 更新年龄
   * @param instanceData 实例数据
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateAge(instanceData: VegetationInstanceData, deltaTime: number): void {
    // 更新年龄（转换为天数）
    instanceData.age += deltaTime / (60 * 60 * 24) * this.growthSpeedMultiplier;

    // 检查是否超过最大寿命
    if (instanceData.age >= instanceData.maxAge) {
      // 植被死亡
      instanceData.health = 0;
      instanceData.growthStage = VegetationGrowthStage.DEAD;

      // 发出植被死亡事件
      this.eventEmitter.emit(VegetationGrowthSystemEventType.VEGETATION_DEATH, instanceData);
    }
  }

  /**
   * 更新健康度
   * @param instanceData 实例数据
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateHealth(instanceData: VegetationInstanceData, deltaTime: number): void {
    // 如果已经死亡，不再更新健康度
    if (instanceData.growthStage === VegetationGrowthStage.DEAD) {
      instanceData.health = 0;
      return;
    }

    // 基础健康度变化
    let healthChange = 0;

    // 年龄影响健康度
    const ageRatio = instanceData.age / instanceData.maxAge;
    if (ageRatio < 0.1) {
      // 幼年期，健康度缓慢上升
      healthChange += 0.001 * deltaTime;
    } else if (ageRatio < 0.7) {
      // 成长期，健康度稳定
      healthChange += 0.0005 * deltaTime;
    } else if (ageRatio < 0.9) {
      // 成熟期，健康度缓慢下降
      healthChange -= 0.0005 * deltaTime;
    } else {
      // 老年期，健康度快速下降
      healthChange -= 0.001 * deltaTime;
    }

    // 季节影响健康度
    if (this.useSeasonalChanges) {
      switch (this.currentSeason) {
        case SeasonType.SPRING:
          // 春季，健康度上升
          healthChange += 0.0005 * deltaTime;
          break;
        case SeasonType.SUMMER:
          // 夏季，健康度稳定
          break;
        case SeasonType.AUTUMN:
          // 秋季，健康度下降
          healthChange -= 0.0005 * deltaTime;
          break;
        case SeasonType.WINTER:
          // 冬季，健康度快速下降
          healthChange -= 0.001 * deltaTime;
          break;
      }
    }

    // 更新健康度
    instanceData.health = Math.max(0, Math.min(1, instanceData.health + healthChange));

    // 如果健康度为0，植被死亡
    if (instanceData.health <= 0) {
      instanceData.growthStage = VegetationGrowthStage.DEAD;

      // 发出植被死亡事件
      this.eventEmitter.emit(VegetationGrowthSystemEventType.VEGETATION_DEATH, instanceData);
    }
  }

  /**
   * 更新生长阶段
   * @param instanceData 实例数据
   */
  private updateGrowthStage(instanceData: VegetationInstanceData): void {
    // 如果已经死亡，不再更新生长阶段
    if (instanceData.growthStage === VegetationGrowthStage.DEAD) {
      return;
    }

    // 根据年龄计算生长阶段
    const ageRatio = instanceData.age / instanceData.maxAge;
    let newGrowthStage: VegetationGrowthStage;

    if (ageRatio < 0.1) {
      newGrowthStage = VegetationGrowthStage.SEED;
    } else if (ageRatio < 0.2) {
      newGrowthStage = VegetationGrowthStage.SEEDLING;
    } else if (ageRatio < 0.7) {
      newGrowthStage = VegetationGrowthStage.JUVENILE;
    } else if (ageRatio < 0.9) {
      newGrowthStage = VegetationGrowthStage.ADULT;
    } else {
      newGrowthStage = VegetationGrowthStage.SENIOR;
    }

    // 如果生长阶段发生变化，发出事件
    if (newGrowthStage !== instanceData.growthStage) {
      const oldGrowthStage = instanceData.growthStage;
      instanceData.growthStage = newGrowthStage;

      // 发出生长阶段变化事件
      this.eventEmitter.emit(VegetationGrowthSystemEventType.GROWTH_STAGE_CHANGED, instanceData, oldGrowthStage, newGrowthStage);
    }
  }

  /**
   * 更新季节性特征
   * @param instanceData 实例数据
   * @param season 季节
   */
  private updateSeasonalFeatures(instanceData: VegetationInstanceData, season: SeasonType): void {
    // 更新开花状态
    instanceData.flowering = instanceData.floweringSeason.includes(season);

    // 更新结果状态
    instanceData.fruiting = instanceData.fruitingSeason.includes(season);

    // 更新落叶状态
    if (!instanceData.evergreen) {
      instanceData.deciduous = instanceData.leafFallSeason.includes(season);
    }
  }

  /**
   * 更新大小
   * @param instanceData 实例数据
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateSize(instanceData: VegetationInstanceData, deltaTime: number): void {
    // 如果已经死亡，不再更新大小
    if (instanceData.growthStage === VegetationGrowthStage.DEAD) {
      return;
    }

    // 根据生长阶段和生长速度计算大小
    const growthFactor = deltaTime * instanceData.growthRate * 0.01 * this.growthSpeedMultiplier;

    switch (instanceData.growthStage) {
      case VegetationGrowthStage.SEED:
        // 种子阶段，大小为最大大小的5%
        instanceData.currentHeight = instanceData.maxHeight * 0.05;
        instanceData.currentWidth = instanceData.maxWidth * 0.05;
        break;
      case VegetationGrowthStage.SEEDLING:
        // 幼苗阶段，大小缓慢增长到最大大小的20%
        instanceData.currentHeight = Math.min(instanceData.currentHeight + growthFactor, instanceData.maxHeight * 0.2);
        instanceData.currentWidth = Math.min(instanceData.currentWidth + growthFactor, instanceData.maxWidth * 0.2);
        break;
      case VegetationGrowthStage.JUVENILE:
        // 幼年阶段，大小快速增长到最大大小的70%
        instanceData.currentHeight = Math.min(instanceData.currentHeight + growthFactor * 2, instanceData.maxHeight * 0.7);
        instanceData.currentWidth = Math.min(instanceData.currentWidth + growthFactor * 2, instanceData.maxWidth * 0.7);
        break;
      case VegetationGrowthStage.ADULT:
        // 成年阶段，大小缓慢增长到最大大小
        instanceData.currentHeight = Math.min(instanceData.currentHeight + growthFactor, instanceData.maxHeight);
        instanceData.currentWidth = Math.min(instanceData.currentWidth + growthFactor, instanceData.maxWidth);
        break;
      case VegetationGrowthStage.SENIOR:
        // 老年阶段，大小不再增长
        break;
    }

    // 更新缩放
    const heightScale = instanceData.currentHeight / instanceData.maxHeight;
    const widthScale = instanceData.currentWidth / instanceData.maxWidth;
    instanceData.currentScale.set(widthScale, heightScale, widthScale);
  }

  /**
   * 更新实例变换
   * @param entity 实体
   * @param component 植被组件
   * @param instanceId 实例ID
   * @param instance 实例
   * @param instanceData 实例数据
   */
  private updateInstanceTransform(
    entity: Entity,
    component: VegetationComponent,
    instanceId: string,
    instance: any,
    instanceData: VegetationInstanceData
  ): void {
    // 更新实例缩放
    instance.scale.copy(instanceData.currentScale);

    // 如果实例有模型，更新模型
    if (instance.userData.model) {
      const model = instance.userData.model as THREE.Object3D;
      model.scale.copy(instanceData.currentScale);
    }

    // 如果是落叶状态，调整透明度
    if (instanceData.deciduous && instanceData.leafFallSeason.includes(this.currentSeason)) {
      // 如果实例有材质，调整透明度
      if (instance.userData.material) {
        const material = instance.userData.material as THREE.Material;
        if (material.transparent) {
          (material as any).opacity = 0.5;
        }
      }
    } else {
      // 恢复透明度
      if (instance.userData.material) {
        const material = instance.userData.material as THREE.Material;
        if (material.transparent) {
          (material as any).opacity = 1.0;
        }
      }
    }
  }

  /**
   * 更新季节
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateSeason(deltaTime: number): void {
    // 更新季节计时器
    this.seasonChangeTimer += deltaTime;
    if (this.seasonChangeTimer >= this.seasonDuration) {
      this.seasonChangeTimer = 0;

      // 切换到下一个季节
      switch (this.currentSeason) {
        case SeasonType.SPRING:
          this.currentSeason = SeasonType.SUMMER;
          break;
        case SeasonType.SUMMER:
          this.currentSeason = SeasonType.AUTUMN;
          break;
        case SeasonType.AUTUMN:
          this.currentSeason = SeasonType.WINTER;
          break;
        case SeasonType.WINTER:
          this.currentSeason = SeasonType.SPRING;
          break;
      }

      Debug.log('VegetationGrowthSystem', `季节变化: ${this.currentSeason}`);
    }
  }

  /**
   * 获取植被类型
   * @param type 植被类型字符串
   * @returns 植被类型枚举
   */
  private getVegetationType(type: string): VegetationType {
    switch (type.toLowerCase()) {
      case 'tree':
        return VegetationType.TREE;
      case 'shrub':
        return VegetationType.SHRUB;
      case 'grass':
        return VegetationType.GRASS;
      case 'flower':
        return VegetationType.FLOWER;
      case 'aquatic':
        return VegetationType.AQUATIC;
      case 'seasonal':
        return VegetationType.SEASONAL;
      default:
        return VegetationType.CUSTOM;
    }
  }

  /**
   * 获取最大寿命
   * @param type 植被类型字符串
   * @returns 最大寿命（天）
   */
  private getMaxAge(type: string): number {
    switch (type.toLowerCase()) {
      case 'tree':
        return 365 * 50; // 50年
      case 'shrub':
        return 365 * 20; // 20年
      case 'grass':
        return 365 * 2; // 2年
      case 'flower':
        return 365 * 1; // 1年
      case 'aquatic':
        return 365 * 3; // 3年
      case 'seasonal':
        return 365 * 1; // 1年
      default:
        return 365 * 10; // 10年
    }
  }

  /**
   * 是否常绿
   * @param type 植被类型字符串
   * @returns 是否常绿
   */
  private isEvergreen(type: string): boolean {
    switch (type.toLowerCase()) {
      case 'tree':
        return Math.random() > 0.5; // 50%的树是常绿的
      case 'shrub':
        return Math.random() > 0.7; // 30%的灌木是常绿的
      case 'grass':
        return false; // 草不是常绿的
      case 'flower':
        return false; // 花不是常绿的
      case 'aquatic':
        return true; // 水生植物是常绿的
      case 'seasonal':
        return false; // 季节性植物不是常绿的
      default:
        return false;
    }
  }

  /**
   * 获取开花季节
   * @param type 植被类型字符串
   * @returns 开花季节
   */
  private getFloweringSeason(type: string): SeasonType[] {
    switch (type.toLowerCase()) {
      case 'tree':
        return [SeasonType.SPRING];
      case 'shrub':
        return [SeasonType.SPRING, SeasonType.SUMMER];
      case 'grass':
        return [];
      case 'flower':
        return [SeasonType.SPRING, SeasonType.SUMMER];
      case 'aquatic':
        return [SeasonType.SUMMER];
      case 'seasonal':
        return [SeasonType.SPRING, SeasonType.SUMMER];
      default:
        return [SeasonType.SPRING];
    }
  }

  /**
   * 获取结果季节
   * @param type 植被类型字符串
   * @returns 结果季节
   */
  private getFruitingSeason(type: string): SeasonType[] {
    switch (type.toLowerCase()) {
      case 'tree':
        return [SeasonType.SUMMER, SeasonType.AUTUMN];
      case 'shrub':
        return [SeasonType.SUMMER, SeasonType.AUTUMN];
      case 'grass':
        return [];
      case 'flower':
        return [SeasonType.SUMMER];
      case 'aquatic':
        return [SeasonType.SUMMER];
      case 'seasonal':
        return [SeasonType.SUMMER, SeasonType.AUTUMN];
      default:
        return [SeasonType.AUTUMN];
    }
  }

  /**
   * 初始化调试可视化
   */
  private initDebugVisualization(): void {
    // 清除现有调试网格
    this.clearDebugMeshes();

    // 获取活跃场景
    const scene = this.world.getActiveScene();
    if (!scene) {
      return;
    }

    // 创建季节指示器
    const seasonIndicator = new THREE.Mesh(
      new THREE.SphereGeometry(1, 16, 16),
      new THREE.MeshBasicMaterial({ color: this.getSeasonColor(this.currentSeason) })
    );
    seasonIndicator.position.set(0, 50, 0);
    seasonIndicator.name = 'season_indicator';

    // 添加到场景
    scene.getThreeScene().add(seasonIndicator);
    this.debugMeshes.push(seasonIndicator);
  }

  /**
   * 更新调试可视化
   */
  private updateDebugVisualization(): void {
    // 获取活跃场景
    const scene = this.world.getActiveScene();
    if (!scene) {
      return;
    }

    // 更新季节指示器
    const seasonIndicator = scene.getThreeScene().getObjectByName('season_indicator') as THREE.Mesh;
    if (seasonIndicator) {
      (seasonIndicator.material as THREE.MeshBasicMaterial).color.set(this.getSeasonColor(this.currentSeason));
    }
  }

  /**
   * 清除调试网格
   */
  private clearDebugMeshes(): void {
    // 获取活跃场景
    const scene = this.world.getActiveScene();
    if (!scene) {
      return;
    }

    // 移除所有调试网格
    for (const mesh of this.debugMeshes) {
      scene.getThreeScene().remove(mesh);
    }

    // 清空调试网格列表
    this.debugMeshes = [];
  }

  /**
   * 获取季节颜色
   * @param season 季节
   * @returns 颜色
   */
  private getSeasonColor(season: SeasonType): number {
    switch (season) {
      case SeasonType.SPRING:
        return 0x00ff00; // 绿色
      case SeasonType.SUMMER:
        return 0xffff00; // 黄色
      case SeasonType.AUTUMN:
        return 0xff8000; // 橙色
      case SeasonType.WINTER:
        return 0x0080ff; // 蓝色
      default:
        return 0xffffff; // 白色
    }
  }
}