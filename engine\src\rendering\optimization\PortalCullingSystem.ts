/**
 * 门户剔除系统
 * 用于室内场景的高效剔除，通过门户（如门、窗口等）连接不同的房间
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { Camera   } from '../Camera';
import { Scene } from '../../scene/Scene';
import type { Transform } from '../../scene/Transform';
import { CullableComponent } from './CullableComponent';
import { Debug } from '../../utils/Debug';

/**
 * 门户剔除系统配置接口
 */
export interface PortalCullingSystemOptions {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
  /** 是否使用保守剔除 */
  useConservativeCulling?: boolean;
  /** 是否使用时间一致性 */
  useTemporalCoherence?: boolean;
  /** 是否使用门户合并 */
  usePortalMerging?: boolean;
  /** 是否使用门户缓存 */
  usePortalCache?: boolean;
  /** 门户缓存更新间隔（帧） */
  portalCacheUpdateInterval?: number;
  /** 是否使用抗锯齿 */
  useAntiAliasing?: boolean;
}

/**
 * 门户接口
 */
export interface Portal {
  /** 门户ID */
  id: string;
  /** 门户几何体 */
  geometry: THREE.BufferGeometry;
  /** 门户位置 */
  position: THREE.Vector3;
  /** 门户旋转 */
  rotation: THREE.Euler;
  /** 门户缩放 */
  scale: THREE.Vector3;
  /** 门户矩阵 */
  matrix: THREE.Matrix4;
  /** 门户法线 */
  normal: THREE.Vector3;
  /** 门户中心 */
  center: THREE.Vector3;
  /** 门户半宽 */
  halfWidth: number;
  /** 门户半高 */
  halfHeight: number;
  /** 门户连接的房间A */
  roomA: Room;
  /** 门户连接的房间B */
  roomB: Room;
  /** 门户是否可见 */
  visible: boolean;
  /** 门户是否双向 */
  bidirectional: boolean;
  /** 门户是否开启 */
  open: boolean;
  /** 门户开启程度（0-1） */
  openness: number;
  /** 门户视锥体 */
  frustum?: THREE.Frustum;
  /** 门户平面 */
  plane?: THREE.Plane;
}

/**
 * 房间接口
 */
export interface Room {
  /** 房间ID */
  id: string;
  /** 房间几何体 */
  geometry: THREE.BufferGeometry;
  /** 房间位置 */
  position: THREE.Vector3;
  /** 房间旋转 */
  rotation: THREE.Euler;
  /** 房间缩放 */
  scale: THREE.Vector3;
  /** 房间矩阵 */
  matrix: THREE.Matrix4;
  /** 房间包围盒 */
  boundingBox: THREE.Box3;
  /** 房间包围球 */
  boundingSphere: THREE.Sphere;
  /** 房间门户列表 */
  portals: Portal[];
  /** 房间实体列表 */
  entities: Entity[];
  /** 房间是否可见 */
  visible: boolean;
  /** 房间是否已访问 */
  visited: boolean;
  /** 房间深度（从相机所在房间开始） */
  depth: number;
}

/**
 * 门户剔除结果接口
 */
export interface PortalCullingResult {
  /** 可见房间数量 */
  visibleRoomCount: number;
  /** 不可见房间数量 */
  invisibleRoomCount: number;
  /** 可见门户数量 */
  visiblePortalCount: number;
  /** 不可见门户数量 */
  invisiblePortalCount: number;
  /** 可见实体数量 */
  visibleEntityCount: number;
  /** 不可见实体数量 */
  invisibleEntityCount: number;
  /** 总房间数量 */
  totalRoomCount: number;
  /** 总门户数量 */
  totalPortalCount: number;
  /** 总实体数量 */
  totalEntityCount: number;
  /** 房间剔除率 */
  roomCullingRate: number;
  /** 门户剔除率 */
  portalCullingRate: number;
  /** 实体剔除率 */
  entityCullingRate: number;
  /** 剔除时间（毫秒） */
  cullingTime: number;
}

/**
 * 门户剔除系统类
 */
export class PortalCullingSystem extends System {
  /** 系统类型 */
  private static readonly TYPE: string = 'PortalCullingSystem';

  /** 是否启用 */
  private enabled: boolean;

  /** 是否使用调试可视化 */
  private useDebugVisualization: boolean;

  /** 是否使用保守剔除 */
  private useConservativeCulling: boolean;

  /** 是否使用时间一致性 */
  private useTemporalCoherence: boolean;

  /** 是否使用门户合并 */
  private usePortalMerging: boolean;

  /** 是否使用门户缓存 */
  private usePortalCache: boolean;

  /** 门户缓存更新间隔（帧） */
  private portalCacheUpdateInterval: number;

  /** 是否使用抗锯齿 */
  private useAntiAliasing: boolean;

  /** 当前帧计数 */
  private frameCount: number = 0;

  /** 房间列表 */
  private rooms: Map<string, Room> = new Map();

  /** 门户列表 */
  private portals: Map<string, Portal> = new Map();

  /** 实体到房间的映射 */
  private entityToRoom: Map<Entity, Room> = new Map();

  /** 相机所在房间 */
  private cameraRoom: Room | null = null;

  /** 上一帧相机所在房间 */
  private previousCameraRoom: Room | null = null;

  /** 可见房间列表 */
  private visibleRooms: Set<Room> = new Set();

  /** 可见门户列表 */
  private visiblePortals: Set<Portal> = new Set();

  /** 门户视锥体缓存 */
  private portalFrustumCache: Map<Portal, THREE.Frustum> = new Map();

  /** 调试可视化材质 */
  private debugMaterial: THREE.MeshBasicMaterial | null = null;

  /** 调试可视化网格 */
  private debugMeshes: THREE.Mesh[] = [];

  /** 剔除时间 */
  private cullingTime: number = 0;

  /**
   * 创建门户剔除系统
   * @param options 门户剔除系统配置
   */
  constructor(options: PortalCullingSystemOptions = {}) {
    super();

    this.enabled = options.enabled !== undefined ? options.enabled : true;
    this.useDebugVisualization = options.useDebugVisualization !== undefined ? options.useDebugVisualization : false;
    this.useConservativeCulling = options.useConservativeCulling !== undefined ? options.useConservativeCulling : true;
    this.useTemporalCoherence = options.useTemporalCoherence !== undefined ? options.useTemporalCoherence : true;
    this.usePortalMerging = options.usePortalMerging !== undefined ? options.usePortalMerging : true;
    this.usePortalCache = options.usePortalCache !== undefined ? options.usePortalCache : true;
    this.portalCacheUpdateInterval = options.portalCacheUpdateInterval || 5;
    this.useAntiAliasing = options.useAntiAliasing !== undefined ? options.useAntiAliasing : true;
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    // 初始化调试可视化
    if (this.useDebugVisualization) {
      this.initializeDebugVisualization();
    }
  }

  /**
   * 初始化调试可视化
   */
  private initializeDebugVisualization(): void {
    // 创建调试材质
    this.debugMaterial = new THREE.MeshBasicMaterial({
      wireframe: true,
      transparent: true,
      opacity: 0.5
    });
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.enabled) {
      return;
    }

    // 增加帧计数
    this.frameCount++;

    // 获取相机和场景
    const camera = this.getCamera();
    const scene = this.getScene();

    if (!camera || !scene) {
      return;
    }

    // 记录开始时间
    const startTime = performance.now();

    // 更新相机所在房间
    this.updateCameraRoom(camera);

    // 如果相机不在任何房间中，则不进行剔除
    if (!this.cameraRoom) {
      return;
    }

    // 清除可见房间和门户
    this.visibleRooms.clear();
    this.visiblePortals.clear();

    // 将相机所在房间标记为可见
    this.visibleRooms.add(this.cameraRoom);
    this.cameraRoom.visible = true;
    this.cameraRoom.visited = true;
    this.cameraRoom.depth = 0;

    // 执行门户剔除
    this.cullWithPortals(camera, scene);

    // 更新实体可见性
    this.updateEntityVisibility();

    // 记录剔除时间
    this.cullingTime = performance.now() - startTime;

    // 更新调试可视化
    if (this.useDebugVisualization) {
      this.updateDebugVisualization();
    }
  }

  /**
   * 获取相机
   * @returns 相机
   */
  private getCamera(): Camera | null {
    // 获取相机组件
    const cameras = this.entityManager.getComponentsOfType<Camera>('Camera');
    if (cameras.length === 0) {
      return null;
    }

    // 返回第一个相机
    return cameras[0];
  }

  /**
   * 获取场景
   * @returns 场景
   */
  private getScene(): Scene | null {
    // 获取场景组件
    const scenes = this.entityManager.getComponentsOfType<Scene>('Scene');
    if (scenes.length === 0) {
      return null;
    }

    // 返回第一个场景
    return scenes[0];
  }

  /**
   * 更新相机所在房间
   * @param camera 相机
   */
  private updateCameraRoom(camera: Camera): void {
    // 保存上一帧相机所在房间
    this.previousCameraRoom = this.cameraRoom;

    // 获取相机位置
    const cameraPosition = camera.getObject3D().position;

    // 查找相机所在房间
    this.cameraRoom = null;
    for (const room of this.rooms.values()) {
      if (this.isPointInRoom(cameraPosition, room)) {
        this.cameraRoom = room;
        break;
      }
    }

    // 如果没有找到相机所在房间，则尝试使用上一帧的房间
    if (!this.cameraRoom && this.previousCameraRoom && this.useTemporalCoherence) {
      if (this.isPointInRoom(cameraPosition, this.previousCameraRoom)) {
        this.cameraRoom = this.previousCameraRoom;
      }
    }
  }

  /**
   * 检查点是否在房间内
   * @param point 点
   * @param room 房间
   * @returns 是否在房间内
   */
  private isPointInRoom(point: THREE.Vector3, room: Room): boolean {
    // 检查点是否在房间包围盒内
    if (!room.boundingBox.containsPoint(point)) {
      return false;
    }

    // 在实际项目中，这里应该使用更精确的方法检查点是否在房间内
    // 例如，使用射线检测或多边形包含测试
    // 这里使用简化的实现
    return true;
  }

  /**
   * 使用门户进行剔除
   * @param camera 相机
   * @param scene 场景
   */
  private cullWithPortals(camera: Camera, scene: Scene): void {
    // 创建初始视锥体
    const initialFrustum = new THREE.Frustum().setFromProjectionMatrix(
      new THREE.Matrix4().multiplyMatrices(
        camera.getObject3D().projectionMatrix,
        camera.getObject3D().matrixWorldInverse
      )
    );

    // 递归遍历可见房间和门户
    this.traversePortals(camera, initialFrustum, this.cameraRoom!, 0);
  }

  /**
   * 递归遍历门户
   * @param camera 相机
   * @param parentFrustum 父视锥体
   * @param currentRoom 当前房间
   * @param depth 深度
   */
  private traversePortals(camera: Camera, parentFrustum: THREE.Frustum, currentRoom: Room, depth: number): void {
    // 如果深度过大，则停止遍历
    if (depth > 10) {
      return;
    }

    // 遍历当前房间的所有门户
    for (const portal of currentRoom.portals) {
      // 如果门户不可见或已关闭，则跳过
      if (!portal.visible || !portal.open) {
        continue;
      }

      // 检查门户是否在视锥体内
      if (!this.isPortalInFrustum(portal, parentFrustum)) {
        continue;
      }

      // 获取门户连接的另一个房间
      const nextRoom = portal.roomA === currentRoom ? portal.roomB : portal.roomA;

      // 如果房间已经可见，则跳过
      if (this.visibleRooms.has(nextRoom)) {
        continue;
      }

      // 计算门户视锥体
      const portalFrustum = this.computePortalFrustum(camera, portal);
      if (!portalFrustum) {
        continue;
      }

      // 将门户和房间标记为可见
      this.visiblePortals.add(portal);
      this.visibleRooms.add(nextRoom);
      nextRoom.visible = true;
      nextRoom.visited = true;
      nextRoom.depth = depth + 1;

      // 递归遍历下一个房间
      this.traversePortals(camera, portalFrustum, nextRoom, depth + 1);
    }
  }

  /**
   * 检查门户是否在视锥体内
   * @param portal 门户
   * @param frustum 视锥体
   * @returns 是否在视锥体内
   */
  private isPortalInFrustum(portal: Portal, frustum: THREE.Frustum): boolean {
    // 检查门户中心是否在视锥体内
    if (frustum.containsPoint(portal.center)) {
      return true;
    }

    // 在实际项目中，这里应该使用更精确的方法检查门户是否在视锥体内
    // 例如，检查门户的四个角点是否有任何一个在视锥体内
    // 这里使用简化的实现
    return false;
  }

  /**
   * 计算门户视锥体
   * @param camera 相机
   * @param portal 门户
   * @returns 门户视锥体
   */
  private computePortalFrustum(camera: Camera, portal: Portal): THREE.Frustum | null {
    // 如果使用门户缓存且不需要更新，则返回缓存的视锥体
    if (this.usePortalCache && this.portalFrustumCache.has(portal) && this.frameCount % this.portalCacheUpdateInterval !== 0) {
      return this.portalFrustumCache.get(portal)!;
    }

    // 在实际项目中，这里应该计算从相机通过门户看到的视锥体
    // 这里使用简化的实现
    const frustum = new THREE.Frustum();

    // 缓存视锥体
    if (this.usePortalCache) {
      this.portalFrustumCache.set(portal, frustum);
    }

    return frustum;
  }

  /**
   * 更新实体可见性
   */
  private updateEntityVisibility(): void {
    // 获取可剔除组件
    const cullableComponents = this.entityManager.getComponentsOfType<CullableComponent>('CullableComponent');
    if (cullableComponents.length === 0) {
      return;
    }

    // 遍历可剔除组件
    for (const component of cullableComponents) {
      const entity = component.getEntity();
      if (!entity) {
        continue;
      }

      // 获取实体所在房间
      const room = this.entityToRoom.get(entity);
      if (!room) {
        continue;
      }

      // 更新可见性
      component.setVisible(room.visible);
    }
  }

  /**
   * 更新调试可视化
   */
  private updateDebugVisualization(): void {
    // 清除调试网格
    for (const mesh of this.debugMeshes) {
      mesh.parent?.remove(mesh);
    }
    this.debugMeshes = [];

    // 获取场景
    const scene = this.getScene();
    if (!scene) {
      return;
    }

    // 可视化房间
    for (const room of this.rooms.values()) {
      // 创建房间调试网格
      const roomMesh = new THREE.Mesh(
        new THREE.BoxBufferGeometry(
          room.boundingBox.max.x - room.boundingBox.min.x,
          room.boundingBox.max.y - room.boundingBox.min.y,
          room.boundingBox.max.z - room.boundingBox.min.z
        ),
        this.debugMaterial!.clone()
      );

      // 设置房间调试网格颜色
      if (room === this.cameraRoom) {
        (roomMesh.material as THREE.MeshBasicMaterial).color.set(0x00ffff); // 青色
      } else if (room.visible) {
        (roomMesh.material as THREE.MeshBasicMaterial).color.set(0x00ff00); // 绿色
      } else {
        (roomMesh.material as THREE.MeshBasicMaterial).color.set(0xff0000); // 红色
      }

      // 设置房间调试网格位置
      roomMesh.position.copy(room.position);
      roomMesh.rotation.copy(room.rotation);
      roomMesh.scale.copy(room.scale);

      // 添加到场景
      scene.getObject3D().add(roomMesh);
      this.debugMeshes.push(roomMesh);
    }

    // 可视化门户
    for (const portal of this.portals.values()) {
      // 创建门户调试网格
      const portalGeometry = new THREE.PlaneBufferGeometry(portal.halfWidth * 2, portal.halfHeight * 2);
      const portalMesh = new THREE.Mesh(portalGeometry, this.debugMaterial!.clone());

      // 设置门户调试网格颜色
      if (this.visiblePortals.has(portal)) {
        (portalMesh.material as THREE.MeshBasicMaterial).color.set(0xffff00); // 黄色
      } else {
        (portalMesh.material as THREE.MeshBasicMaterial).color.set(0xff00ff); // 紫色
      }

      // 设置门户调试网格位置
      portalMesh.position.copy(portal.position);
      portalMesh.rotation.copy(portal.rotation);
      portalMesh.scale.copy(portal.scale);

      // 添加到场景
      scene.getObject3D().add(portalMesh);
      this.debugMeshes.push(portalMesh);
    }
  }

  /**
   * 添加房间
   * @param room 房间
   */
  public addRoom(room: Room): void {
    // 添加房间
    this.rooms.set(room.id, room);

    // 初始化房间
    room.visible = false;
    room.visited = false;
    room.depth = -1;
    room.portals = room.portals || [];
    room.entities = room.entities || [];

    // 计算房间包围盒和包围球
    if (!room.boundingBox) {
      room.boundingBox = new THREE.Box3();
      room.boundingBox.setFromObject(new THREE.Mesh(room.geometry));
    }

    if (!room.boundingSphere) {
      room.boundingSphere = new THREE.Sphere();
      room.boundingBox.getBoundingSphere(room.boundingSphere);
    }

    // 更新实体到房间的映射
    for (const entity of room.entities) {
      this.entityToRoom.set(entity, room);
    }
  }

  /**
   * 移除房间
   * @param roomId 房间ID
   */
  public removeRoom(roomId: string): void {
    // 获取房间
    const room = this.rooms.get(roomId);
    if (!room) {
      return;
    }

    // 移除实体到房间的映射
    for (const entity of room.entities) {
      this.entityToRoom.delete(entity);
    }

    // 移除房间的门户
    for (const portal of room.portals) {
      this.portals.delete(portal.id);
      this.portalFrustumCache.delete(portal);
    }

    // 移除房间
    this.rooms.delete(roomId);

    // 如果移除的是相机所在房间，则清除相机所在房间
    if (room === this.cameraRoom) {
      this.cameraRoom = null;
    }
  }

  /**
   * 添加门户
   * @param portal 门户
   */
  public addPortal(portal: Portal): void {
    // 添加门户
    this.portals.set(portal.id, portal);

    // 初始化门户
    portal.visible = true;
    portal.open = true;
    portal.openness = 1.0;

    // 计算门户法线和中心
    if (!portal.normal) {
      portal.normal = new THREE.Vector3(0, 0, 1).applyEuler(portal.rotation);
    }

    if (!portal.center) {
      portal.center = portal.position.clone();
    }

    // 添加门户到房间
    if (portal.roomA && !portal.roomA.portals.includes(portal)) {
      portal.roomA.portals.push(portal);
    }

    if (portal.roomB && !portal.roomB.portals.includes(portal)) {
      portal.roomB.portals.push(portal);
    }

    // 创建门户平面
    portal.plane = new THREE.Plane().setFromNormalAndCoplanarPoint(portal.normal, portal.center);
  }

  /**
   * 移除门户
   * @param portalId 门户ID
   */
  public removePortal(portalId: string): void {
    // 获取门户
    const portal = this.portals.get(portalId);
    if (!portal) {
      return;
    }

    // 从房间中移除门户
    if (portal.roomA) {
      const index = portal.roomA.portals.indexOf(portal);
      if (index !== -1) {
        portal.roomA.portals.splice(index, 1);
      }
    }

    if (portal.roomB) {
      const index = portal.roomB.portals.indexOf(portal);
      if (index !== -1) {
        portal.roomB.portals.splice(index, 1);
      }
    }

    // 移除门户缓存
    this.portalFrustumCache.delete(portal);

    // 移除门户
    this.portals.delete(portalId);
  }

  /**
   * 添加实体到房间
   * @param entity 实体
   * @param roomId 房间ID
   */
  public addEntityToRoom(entity: Entity, roomId: string): void {
    // 获取房间
    const room = this.rooms.get(roomId);
    if (!room) {
      return;
    }

    // 从旧房间中移除实体
    const oldRoom = this.entityToRoom.get(entity);
    if (oldRoom) {
      const index = oldRoom.entities.indexOf(entity);
      if (index !== -1) {
        oldRoom.entities.splice(index, 1);
      }
    }

    // 添加实体到房间
    if (!room.entities.includes(entity)) {
      room.entities.push(entity);
    }

    // 更新实体到房间的映射
    this.entityToRoom.set(entity, room);
  }

  /**
   * 从房间中移除实体
   * @param entity 实体
   */
  public removeEntityFromRoom(entity: Entity): void {
    // 获取实体所在房间
    const room = this.entityToRoom.get(entity);
    if (!room) {
      return;
    }

    // 从房间中移除实体
    const index = room.entities.indexOf(entity);
    if (index !== -1) {
      room.entities.splice(index, 1);
    }

    // 移除实体到房间的映射
    this.entityToRoom.delete(entity);
  }

  /**
   * 设置门户开启状态
   * @param portalId 门户ID
   * @param open 是否开启
   * @param openness 开启程度（0-1）
   */
  public setPortalOpen(portalId: string, open: boolean, openness: number = 1.0): void {
    // 获取门户
    const portal = this.portals.get(portalId);
    if (!portal) {
      return;
    }

    // 设置门户开启状态
    portal.open = open;
    portal.openness = Math.max(0, Math.min(1, openness));
  }

  /**
   * 获取门户剔除结果
   * @returns 门户剔除结果
   */
  public getPortalCullingResult(): PortalCullingResult {
    // 统计可见和不可见的房间、门户和实体
    let visibleRoomCount = 0;
    let invisibleRoomCount = 0;
    let visiblePortalCount = 0;
    let invisiblePortalCount = 0;
    let visibleEntityCount = 0;
    let invisibleEntityCount = 0;

    // 统计房间
    for (const room of this.rooms.values()) {
      if (room.visible) {
        visibleRoomCount++;
      } else {
        invisibleRoomCount++;
      }
    }

    // 统计门户
    for (const portal of this.portals.values()) {
      if (this.visiblePortals.has(portal)) {
        visiblePortalCount++;
      } else {
        invisiblePortalCount++;
      }
    }

    // 统计实体
    for (const [entity, room] of this.entityToRoom.entries()) {
      if (room.visible) {
        visibleEntityCount++;
      } else {
        invisibleEntityCount++;
      }
    }

    // 计算总数和剔除率
    const totalRoomCount = visibleRoomCount + invisibleRoomCount;
    const totalPortalCount = visiblePortalCount + invisiblePortalCount;
    const totalEntityCount = visibleEntityCount + invisibleEntityCount;

    const roomCullingRate = totalRoomCount > 0 ? invisibleRoomCount / totalRoomCount : 0;
    const portalCullingRate = totalPortalCount > 0 ? invisiblePortalCount / totalPortalCount : 0;
    const entityCullingRate = totalEntityCount > 0 ? invisibleEntityCount / totalEntityCount : 0;

    return {
      visibleRoomCount,
      invisibleRoomCount,
      visiblePortalCount,
      invisiblePortalCount,
      visibleEntityCount,
      invisibleEntityCount,
      totalRoomCount,
      totalPortalCount,
      totalEntityCount,
      roomCullingRate,
      portalCullingRate,
      entityCullingRate,
      cullingTime: this.cullingTime
    };
  }

  /**
   * 设置是否启用
   * @param enabled 是否启用
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }

  /**
   * 获取是否启用
   * @returns 是否启用
   */
  public isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * 清除所有房间和门户
   */
  public clear(): void {
    // 清除房间和门户
    this.rooms.clear();
    this.portals.clear();
    this.entityToRoom.clear();
    this.portalFrustumCache.clear();
    this.visibleRooms.clear();
    this.visiblePortals.clear();
    this.cameraRoom = null;
    this.previousCameraRoom = null;
  }

  /**
   * 销毁系统
   */
  public destroy(): void {
    // 清除所有房间和门户
    this.clear();

    // 清除调试网格
    for (const mesh of this.debugMeshes) {
      mesh.parent?.remove(mesh);
      (mesh.geometry as any).dispose();
      (mesh.material as THREE.Material).dispose();
    }
    this.debugMeshes = [];

    // 清除调试材质
    if (this.debugMaterial) {
      (this.debugMaterial as any).dispose();
      this.debugMaterial = null;
    }
  }
}
