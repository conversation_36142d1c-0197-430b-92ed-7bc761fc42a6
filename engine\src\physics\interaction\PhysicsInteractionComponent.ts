/**
 * 物理交互组件
 * 用于实现角色与物理对象的交互
 */
import * as THREE from 'three';

import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { type PhysicsBody  } from '../PhysicsBody';
import { PhysicsCollider } from '../PhysicsCollider';
import { Debug } from '../../utils/Debug';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 交互类型枚举
 */
export enum InteractionType {
  /** 推动 */
  PUSH = 'push',
  /** 拉动 */
  PULL = 'pull',
  /** 举起 */
  LIFT = 'lift',
  /** 投掷 */
  THROW = 'throw',
  /** 攀爬 */
  CLIMB = 'climb',
  /** 悬挂 */
  HANG = 'hang'
}

/**
 * 物理交互组件配置
 */
export interface PhysicsInteractionComponentConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 最大交互距离 */
  maxInteractionDistance?: number;
  /** 交互力大小 */
  interactionForce?: number;
  /** 交互力衰减系数 */
  interactionForceDamping?: number;
  /** 允许的交互类型 */
  allowedInteractionTypes?: InteractionType[];
  /** 是否可以被推动 */
  canBePushed?: boolean;
  /** 是否可以被拉动 */
  canBePulled?: boolean;
  /** 是否可以被举起 */
  canBeLifted?: boolean;
  /** 是否可以被投掷 */
  canBeThrown?: boolean;
  /** 是否可以被攀爬 */
  canBeClimbed?: boolean;
  /** 是否可以被悬挂 */
  canBeHanged?: boolean;
  /** 交互开始回调 */
  onInteractionStart?: (entity: Entity, interactor: Entity, type: InteractionType) => void;
  /** 交互结束回调 */
  onInteractionEnd?: (entity: Entity, interactor: Entity, type: InteractionType) => void;
}

/**
 * 物理交互组件
 * 用于实现角色与物理对象的交互
 */
export class PhysicsInteractionComponent extends Component {
  /** 组件类型 */
  public static readonly type: string = 'PhysicsInteractionComponent';

  /** 是否启用 */
  private _enabled: boolean;

  /** 最大交互距离 */
  private _maxInteractionDistance: number;

  /** 交互力大小 */
  private _interactionForce: number;

  /** 交互力衰减系数 */
  private _interactionForceDamping: number;

  /** 允许的交互类型 */
  private _allowedInteractionTypes: Set<InteractionType>;

  /** 是否可以被推动 */
  private _canBePushed: boolean;

  /** 是否可以被拉动 */
  private _canBePulled: boolean;

  /** 是否可以被举起 */
  private _canBeLifted: boolean;

  /** 是否可以被投掷 */
  private _canBeThrown: boolean;

  /** 是否可以被攀爬 */
  private _canBeClimbed: boolean;

  /** 是否可以被悬挂 */
  private _canBeHanged: boolean;

  /** 当前交互者 */
  private _currentInteractors: Map<Entity, InteractionType> = new Map();

  /** 事件发射器 */
  private _eventEmitter: EventEmitter = new EventEmitter();

  /** 交互开始回调 */
  private _onInteractionStart: ((entity: Entity, interactor: Entity, type: InteractionType) => void) | null = null;

  /** 交互结束回调 */
  private _onInteractionEnd: ((entity: Entity, interactor: Entity, type: InteractionType) => void) | null = null;

  /** 是否已初始化 */
  private _initialized: boolean = false;

  /** 是否已销毁 */
  private _destroyed: boolean = false;

  /**
   * 创建物理交互组件
   * @param config 组件配置
   */
  constructor(config: PhysicsInteractionComponentConfig = {}) {
    super(PhysicsInteractionComponent.type);

    // 设置默认配置
    this._enabled = config.enabled !== undefined ? config.enabled : true;
    this._maxInteractionDistance = config.maxInteractionDistance || 2.0;
    this._interactionForce = config.interactionForce || 500.0;
    this._interactionForceDamping = config.interactionForceDamping || 0.5;

    // 设置允许的交互类型
    this._allowedInteractionTypes = new Set(config.allowedInteractionTypes || [
      InteractionType.PUSH,
      InteractionType.PULL,
      InteractionType.LIFT,
      InteractionType.THROW
    ]);

    // 设置交互标志
    this._canBePushed = config.canBePushed !== undefined ? config.canBePushed : true;
    this._canBePulled = config.canBePulled !== undefined ? config.canBePulled : true;
    this._canBeLifted = config.canBeLifted !== undefined ? config.canBeLifted : true;
    this._canBeThrown = config.canBeThrown !== undefined ? config.canBeThrown : true;
    this._canBeClimbed = config.canBeClimbed !== undefined ? config.canBeClimbed : false;
    this._canBeHanged = config.canBeHanged !== undefined ? config.canBeHanged : false;

    // 设置回调
    this._onInteractionStart = config.onInteractionStart || null;
    this._onInteractionEnd = config.onInteractionEnd || null;
  }

  /**
   * 初始化组件
   */
  public initialize(): void {
    if (this._initialized || !this.entity || this._destroyed) return;

    // 检查实体是否有物理体组件
    const physicsBody = this.entity.getComponent(PhysicsBody.type) as PhysicsBody | null;
    if (!physicsBody) {
      Debug.warn('PhysicsInteractionComponent', `实体 ${this.entity.id} 没有物理体组件，交互功能可能受限`);
    }

    // 检查实体是否有碰撞器组件
    const physicsCollider = this.entity.getComponent(PhysicsCollider.type) as PhysicsCollider | null;
    if (!physicsCollider) {
      Debug.warn('PhysicsInteractionComponent', `实体 ${this.entity.id} 没有碰撞器组件，交互功能可能受限`);
    }

    this._initialized = true;
  }

  /**
   * 更新组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this._enabled || !this._initialized || this._destroyed) return;

    // 更新当前交互
    for (const [interactor, interactionType] of this._currentInteractors.entries()) {
      this.updateInteraction(interactor, interactionType, deltaTime);
    }
  }

  /**
   * 更新交互
   * @param interactor 交互者
   * @param interactionType 交互类型
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateInteraction(interactor: Entity, interactionType: InteractionType, deltaTime: number): void {
    // 获取物理体组件
    const physicsBody = this.entity.getComponent(PhysicsBody.type) as PhysicsBody | null;
    if (!physicsBody) return;

    // 获取交互者的变换组件
    const interactorTransform = interactor.getTransform();
    if (!interactorTransform) return;

    // 获取实体的变换组件
    const entityTransform = this.entity.getTransform();
    if (!entityTransform) return;

    // 根据交互类型执行不同的交互逻辑
    switch (interactionType) {
      case InteractionType.PUSH:
        this.handlePushInteraction(interactor, physicsBody, interactorTransform, entityTransform, deltaTime);
        break;
      case InteractionType.PULL:
        this.handlePullInteraction(interactor, physicsBody, interactorTransform, entityTransform, deltaTime);
        break;
      case InteractionType.LIFT:
        this.handleLiftInteraction(interactor, physicsBody, interactorTransform, entityTransform, deltaTime);
        break;
      case InteractionType.THROW:
        this.handleThrowInteraction(interactor, physicsBody, interactorTransform, entityTransform, deltaTime);
        break;
      case InteractionType.CLIMB:
        this.handleClimbInteraction(interactor, physicsBody, interactorTransform, entityTransform, deltaTime);
        break;
      case InteractionType.HANG:
        this.handleHangInteraction(interactor, physicsBody, interactorTransform, entityTransform, deltaTime);
        break;
    }
  }

  /**
   * 处理推动交互
   * @param interactor 交互者
   * @param physicsBody 物理体组件
   * @param interactorTransform 交互者变换组件
   * @param entityTransform 实体变换组件
   * @param deltaTime 帧间隔时间（秒）
   */
  private handlePushInteraction(
    _interactor: Entity,
    physicsBody: PhysicsBody,
    interactorTransform: any,
    entityTransform: any,
    deltaTime: number
  ): void {
    if (!this._canBePushed) return;

    // 获取交互者位置
    const interactorPosition = interactorTransform.getWorldPosition();

    // 获取实体位置
    const entityPosition = entityTransform.getWorldPosition();

    // 计算方向向量
    const direction = new THREE.Vector3()
      .subVectors(entityPosition, interactorPosition)
      .normalize();

    // 计算力大小
    const force = direction.clone().multiplyScalar(this._interactionForce * deltaTime);

    // 应用力
    physicsBody.applyForce({ x: force.x, y: force.y, z: force.z });
  }

  /**
   * 处理拉动交互
   * @param interactor 交互者
   * @param physicsBody 物理体组件
   * @param interactorTransform 交互者变换组件
   * @param entityTransform 实体变换组件
   * @param deltaTime 帧间隔时间（秒）
   */
  private handlePullInteraction(
    _interactor: Entity,
    physicsBody: PhysicsBody,
    interactorTransform: any,
    entityTransform: any,
    deltaTime: number
  ): void {
    if (!this._canBePulled) return;

    // 获取交互者位置
    const interactorPosition = interactorTransform.getWorldPosition();

    // 获取实体位置
    const entityPosition = entityTransform.getWorldPosition();

    // 计算方向向量
    const direction = new THREE.Vector3()
      .subVectors(interactorPosition, entityPosition)
      .normalize();

    // 计算力大小
    const force = direction.clone().multiplyScalar(this._interactionForce * deltaTime);

    // 应用力
    physicsBody.applyForce({ x: force.x, y: force.y, z: force.z });
  }

  /**
   * 处理举起交互
   * @param interactor 交互者
   * @param physicsBody 物理体组件
   * @param interactorTransform 交互者变换组件
   * @param entityTransform 实体变换组件
   * @param deltaTime 帧间隔时间（秒）
   */
  private handleLiftInteraction(
    _interactor: Entity,
    physicsBody: PhysicsBody,
    interactorTransform: any,
    entityTransform: any,
    deltaTime: number
  ): void {
    if (!this._canBeLifted) return;

    // 获取交互者位置
    const interactorPosition = interactorTransform.getWorldPosition();

    // 计算目标位置（交互者前方和上方）
    const interactorForward = new THREE.Vector3(0, 0, -1).applyQuaternion(interactorTransform.getWorldQuaternion());
    const targetPosition = interactorPosition.clone()
      .add(interactorForward.multiplyScalar(0.5))
      .add(new THREE.Vector3(0, 1.0, 0));

    // 获取实体位置
    const entityPosition = entityTransform.getWorldPosition();

    // 计算方向向量
    const direction = new THREE.Vector3()
      .subVectors(targetPosition, entityPosition);

    // 计算距离
    const distance = direction.length();
    direction.normalize();

    // 计算力大小（使用弹簧力模型）
    const force = direction.clone().multiplyScalar(this._interactionForce * distance * deltaTime);

    // 应用力
    physicsBody.applyForce({ x: force.x, y: force.y, z: force.z });

    // 应用阻尼力
    const velocity = new THREE.Vector3(
      physicsBody.getLinearVelocity().x,
      physicsBody.getLinearVelocity().y,
      physicsBody.getLinearVelocity().z
    );
    const dampingForce = velocity.clone().multiplyScalar(-this._interactionForceDamping);
    physicsBody.applyForce({ x: dampingForce.x, y: dampingForce.y, z: dampingForce.z });
  }

  /**
   * 处理投掷交互
   * @param interactor 交互者
   * @param physicsBody 物理体组件
   * @param interactorTransform 交互者变换组件
   * @param entityTransform 实体变换组件
   * @param deltaTime 帧间隔时间（秒）
   */
  private handleThrowInteraction(
    interactor: Entity,
    physicsBody: PhysicsBody,
    interactorTransform: any,
    _entityTransform: any,
    _deltaTime: number
  ): void {
    if (!this._canBeThrown) return;

    // 获取交互者前方方向
    const interactorForward = new THREE.Vector3(0, 0, -1).applyQuaternion(interactorTransform.getWorldQuaternion());

    // 计算投掷方向（前方和上方的组合）
    const throwDirection = interactorForward.clone()
      .add(new THREE.Vector3(0, 0.5, 0))
      .normalize();

    // 计算投掷力
    const throwForce = throwDirection.clone().multiplyScalar(this._interactionForce * 2.0);

    // 应用冲量
    physicsBody.applyImpulse({ x: throwForce.x, y: throwForce.y, z: throwForce.z });

    // 投掷后结束交互
    this.endInteraction(interactor);
  }

  /**
   * 处理攀爬交互
   * @param interactor 交互者
   * @param physicsBody 物理体组件
   * @param interactorTransform 交互者变换组件
   * @param entityTransform 实体变换组件
   * @param deltaTime 帧间隔时间（秒）
   */
  private handleClimbInteraction(
    _interactor: Entity,
    _physicsBody: PhysicsBody,
    _interactorTransform: any,
    _entityTransform: any,
    _deltaTime: number
  ): void {
    if (!this._canBeClimbed) return;

    // 攀爬交互的具体实现
    // 这里需要根据具体需求实现攀爬逻辑
  }

  /**
   * 处理悬挂交互
   * @param interactor 交互者
   * @param physicsBody 物理体组件
   * @param interactorTransform 交互者变换组件
   * @param entityTransform 实体变换组件
   * @param deltaTime 帧间隔时间（秒）
   */
  private handleHangInteraction(
    _interactor: Entity,
    _physicsBody: PhysicsBody,
    _interactorTransform: any,
    _entityTransform: any,
    _deltaTime: number
  ): void {
    if (!this._canBeHanged) return;

    // 悬挂交互的具体实现
    // 这里需要根据具体需求实现悬挂逻辑
  }

  /**
   * 开始交互
   * @param interactor 交互者
   * @param interactionType 交互类型
   * @returns 是否成功开始交互
   */
  public startInteraction(interactor: Entity, interactionType: InteractionType): boolean {
    if (!this._enabled || !this._initialized || this._destroyed) {
      return false;
    }

    // 检查是否允许该类型的交互
    if (!this._allowedInteractionTypes.has(interactionType)) {
      Debug.warn('PhysicsInteractionComponent', `实体 ${this.entity.id} 不允许 ${interactionType} 类型的交互`);
      return false;
    }

    // 检查交互类型特定的标志
    switch (interactionType) {
      case InteractionType.PUSH:
        if (!this._canBePushed) return false;
        break;
      case InteractionType.PULL:
        if (!this._canBePulled) return false;
        break;
      case InteractionType.LIFT:
        if (!this._canBeLifted) return false;
        break;
      case InteractionType.THROW:
        if (!this._canBeThrown) return false;
        break;
      case InteractionType.CLIMB:
        if (!this._canBeClimbed) return false;
        break;
      case InteractionType.HANG:
        if (!this._canBeHanged) return false;
        break;
    }

    // 添加到当前交互者
    this._currentInteractors.set(interactor, interactionType);

    // 触发交互开始事件
    this._eventEmitter.emit('interactionStart', this.entity, interactor, interactionType);

    // 调用交互开始回调
    if (this._onInteractionStart) {
      this._onInteractionStart(this.entity, interactor, interactionType);
    }

    return true;
  }

  /**
   * 结束交互
   * @param interactor 交互者
   * @returns 是否成功结束交互
   */
  public endInteraction(interactor: Entity): boolean {
    if (!this._currentInteractors.has(interactor)) {
      return false;
    }

    // 获取交互类型
    const interactionType = this._currentInteractors.get(interactor)!;

    // 从当前交互者中移除
    this._currentInteractors.delete(interactor);

    // 触发交互结束事件
    this._eventEmitter.emit('interactionEnd', this.entity, interactor, interactionType);

    // 调用交互结束回调
    if (this._onInteractionEnd) {
      this._onInteractionEnd(this.entity, interactor, interactionType);
    }

    return true;
  }

  /**
   * 结束所有交互
   */
  public endAllInteractions(): void {
    // 复制当前交互者列表，因为在循环中会修改原列表
    const interactors = Array.from(this._currentInteractors.keys());

    // 结束每个交互
    for (const interactor of interactors) {
      this.endInteraction(interactor);
    }
  }

  /**
   * 检查是否正在与指定实体交互
   * @param interactor 交互者
   * @returns 是否正在交互
   */
  public isInteractingWith(interactor: Entity): boolean {
    return this._currentInteractors.has(interactor);
  }

  /**
   * 获取与指定实体的交互类型
   * @param interactor 交互者
   * @returns 交互类型，如果没有则返回null
   */
  public getInteractionTypeWith(interactor: Entity): InteractionType | null {
    return this._currentInteractors.get(interactor) || null;
  }

  /**
   * 获取当前所有交互者
   * @returns 交互者数组
   */
  public getCurrentInteractors(): Entity[] {
    return Array.from(this._currentInteractors.keys());
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (...args: any[]) => void): void {
    this._eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (...args: any[]) => void): void {
    this._eventEmitter.off(event, callback);
  }

  /**
   * 销毁组件
   */
  public destroy(): void {
    if (this._destroyed) return;

    // 结束所有交互
    this.endAllInteractions();

    // 清除事件监听器
    this._eventEmitter.removeAllListeners();

    // 标记为已销毁
    this._destroyed = true;
  }

  /**
   * 获取是否启用
   */
  public getEnabled(): boolean {
    return this._enabled;
  }

  /**
   * 设置是否启用
   */
  public setEnabled(value: boolean): void {
    this._enabled = value;
  }

  /**
   * 获取最大交互距离
   */
  public get maxInteractionDistance(): number {
    return this._maxInteractionDistance;
  }

  /**
   * 设置最大交互距离
   */
  public set maxInteractionDistance(value: number) {
    this._maxInteractionDistance = value;
  }

  /**
   * 获取交互力大小
   */
  public get interactionForce(): number {
    return this._interactionForce;
  }

  /**
   * 设置交互力大小
   */
  public set interactionForce(value: number) {
    this._interactionForce = value;
  }

  /**
   * 获取交互力衰减系数
   */
  public get interactionForceDamping(): number {
    return this._interactionForceDamping;
  }

  /**
   * 设置交互力衰减系数
   */
  public set interactionForceDamping(value: number) {
    this._interactionForceDamping = value;
  }
}