/**
 * 地形虚拟纹理系统
 * 管理地形虚拟纹理，集成到引擎系统中
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { EntityManager } from '../../core/EntityManager';
import type { Camera   } from '../../rendering/Camera';
import { Scene } from '../../scene/Scene';
import { TerrainComponent } from '../components/TerrainComponent';
import { TerrainVirtualTexturing, VirtualTextureConfig, VirtualTextureEventType } from './TerrainVirtualTexturing';
import { Debug } from '../../utils/Debug';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 地形虚拟纹理系统事件类型
 */
export enum TerrainVirtualTexturingSystemEventType {
  /** 系统初始化 */
  SYSTEM_INITIALIZED = 'system_initialized',
  /** 地形实体添加 */
  TERRAIN_ENTITY_ADDED = 'terrain_entity_added',
  /** 地形实体移除 */
  TERRAIN_ENTITY_REMOVED = 'terrain_entity_removed',
  /** 虚拟纹理更新 */
  VIRTUAL_TEXTURE_UPDATED = 'virtual_texture_updated',
  /** 错误 */
  ERROR = 'error'
}

/**
 * 地形虚拟纹理系统配置
 */
export interface TerrainVirtualTexturingSystemConfig extends VirtualTextureConfig {
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
}

/**
 * 地形虚拟纹理系统
 */
export class TerrainVirtualTexturingSystem extends System {
  /** 系统类型 */
  public static readonly TYPE: string = 'TerrainVirtualTexturingSystem';

  /** 是否启用 */
  private enabled: boolean;
  /** 是否自动更新 */
  private autoUpdate: boolean;
  /** 更新频率 */
  private updateFrequency: number;
  /** 帧计数器 */
  private frameCount: number;

  /** 虚拟纹理 */
  private virtualTexturing: TerrainVirtualTexturing;
  /** 实体管理器 */
  private entityManager: EntityManager;
  /** 事件发射器 */
  private eventEmitter: EventEmitter;
  /** 地形实体映射 */
  private terrainEntities: Map<Entity, TerrainComponent>;
  /** 活跃相机 */
  private activeCamera: Camera | null;
  /** 活跃场景 */
  private activeScene: Scene | null;
  /** 渲染器 */
  private renderer: THREE.WebGLRenderer | null;
  /** 反馈场景 */
  private feedbackScene: THREE.Scene | null;
  /** 反馈相机 */
  private feedbackCamera: THREE.OrthographicCamera | null;
  /** 反馈四边形 */
  private feedbackQuad: THREE.Mesh | null;

  /**
   * 创建地形虚拟纹理系统
   * @param config 配置
   */
  constructor(config: TerrainVirtualTexturingSystemConfig = {}) {
    super();

    // 初始化配置
    this.enabled = config.enabled !== undefined ? config.enabled : true;
    this.autoUpdate = config.autoUpdate !== undefined ? config.autoUpdate : true;
    this.updateFrequency = config.updateFrequency || 1;
    this.frameCount = 0;

    // 创建虚拟纹理
    this.virtualTexturing = new TerrainVirtualTexturing(config);

    // 初始化属性
    this.entityManager = new EntityManager();
    this.eventEmitter = new EventEmitter();
    this.terrainEntities = new Map();
    this.activeCamera = null;
    this.activeScene = null;
    this.renderer = null;
    this.feedbackScene = null;
    this.feedbackCamera = null;
    this.feedbackQuad = null;

    // 注册虚拟纹理事件
    this.registerVirtualTexturingEvents();
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return TerrainVirtualTexturingSystem.TYPE;
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    // 查找活跃相机和场景
    this.findActiveCamera();
    this.findActiveScene();

    // 查找地形实体
    this.findTerrainEntities();

    // 创建反馈渲染设置
    this.createFeedbackRenderSetup();

    // 发出系统初始化事件
    this.eventEmitter.emit(TerrainVirtualTexturingSystemEventType.SYSTEM_INITIALIZED);
  }

  /**
   * 注册虚拟纹理事件
   */
  private registerVirtualTexturingEvents(): void {
    // 页面加载事件
    this.virtualTexturing.on(VirtualTextureEventType.PAGE_LOADED, (pageId: string) => {
      Debug.log('TerrainVirtualTexturingSystem', `页面加载: ${pageId}`);
    });

    // 页面卸载事件
    this.virtualTexturing.on(VirtualTextureEventType.PAGE_UNLOADED, (pageId: string) => {
      Debug.log('TerrainVirtualTexturingSystem', `页面卸载: ${pageId}`);
    });

    // 反馈纹理更新事件
    this.virtualTexturing.on(VirtualTextureEventType.FEEDBACK_TEXTURE_UPDATED, () => {
      this.eventEmitter.emit(TerrainVirtualTexturingSystemEventType.VIRTUAL_TEXTURE_UPDATED);
    });

    // 错误事件
    this.virtualTexturing.on(VirtualTextureEventType.ERROR, (message: string, error: any) => {
      Debug.error('TerrainVirtualTexturingSystem', message, error);
      this.eventEmitter.emit(TerrainVirtualTexturingSystemEventType.ERROR, message, error);
    });
  }

  /**
   * 查找活跃相机
   */
  private findActiveCamera(): void {
    // 获取相机组件
    const cameras = this.entityManager.getComponentsOfType<Camera>('Camera');
    if (cameras.length === 0) {
      Debug.warn('TerrainVirtualTexturingSystem', '没有找到相机');
      return;
    }

    // 设置活跃相机
    this.activeCamera = cameras[0];
  }

  /**
   * 查找活跃场景
   */
  private findActiveScene(): void {
    // 获取场景组件
    const scenes = this.entityManager.getComponentsOfType<Scene>('Scene');
    if (scenes.length === 0) {
      Debug.warn('TerrainVirtualTexturingSystem', '没有找到场景');
      return;
    }

    // 设置活跃场景
    this.activeScene = scenes[0];
  }

  /**
   * 查找地形实体
   */
  private findTerrainEntities(): void {
    // 获取所有地形组件
    const terrainComponents = this.entityManager.getComponentsOfType<TerrainComponent>('TerrainComponent');

    // 如果没有地形组件，则返回
    if (terrainComponents.length === 0) {
      Debug.warn('TerrainVirtualTexturingSystem', '没有找到地形组件');
      return;
    }

    // 添加所有地形实体
    for (const component of terrainComponents) {
      if (component.entity) {
        this.addTerrainEntity(component.entity, component);
      }
    }
  }

  /**
   * 创建反馈渲染设置
   */
  private createFeedbackRenderSetup(): void {
    // 创建反馈场景
    this.feedbackScene = new THREE.Scene();

    // 创建正交相机
    this.feedbackCamera = new THREE.OrthographicCamera(-1, 1, 1, -1, 0, 1);

    // 创建四边形
    const geometry = new THREE.PlaneGeometry(2, 2);
    this.feedbackQuad = new THREE.Mesh(geometry, this.virtualTexturing.getFeedbackMaterial());
    this.feedbackScene.add(this.feedbackQuad);
  }

  /**
   * 添加地形实体
   * @param entity 实体
   * @param component 地形组件
   */
  public addTerrainEntity(entity: Entity, component: TerrainComponent): void {
    // 存储地形实体
    this.terrainEntities.set(entity, component);

    // 发出地形实体添加事件
    this.eventEmitter.emit(TerrainVirtualTexturingSystemEventType.TERRAIN_ENTITY_ADDED, entity, component);

    Debug.log('TerrainVirtualTexturingSystem', `添加地形实体: ${entity.id}`);
  }

  /**
   * 移除地形实体
   * @param entity 实体
   */
  public removeTerrainEntity(entity: Entity): void {
    // 从映射中移除
    this.terrainEntities.delete(entity);

    // 发出地形实体移除事件
    this.eventEmitter.emit(TerrainVirtualTexturingSystemEventType.TERRAIN_ENTITY_REMOVED, entity);

    Debug.log('TerrainVirtualTexturingSystem', `移除地形实体: ${entity.id}`);
  }

  /**
   * 设置渲染器
   * @param renderer 渲染器
   */
  public setRenderer(renderer: THREE.WebGLRenderer): void {
    this.renderer = renderer;
    this.virtualTexturing.setRenderer(renderer);
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.enabled || !this.autoUpdate) {
      return;
    }

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.updateFrequency !== 0) {
      return;
    }

    // 如果没有活跃相机或场景，则尝试查找
    if (!this.activeCamera) {
      this.findActiveCamera();
    }

    if (!this.activeScene) {
      this.findActiveScene();
    }

    // 如果没有渲染器，则返回
    if (!this.renderer) {
      return;
    }

    // 更新反馈纹理
    this.updateFeedbackTexture();

    // 更新虚拟纹理
    this.virtualTexturing.update(deltaTime);
  }

  /**
   * 更新反馈纹理
   */
  private updateFeedbackTexture(): void {
    if (!this.renderer || !this.feedbackScene || !this.feedbackCamera) {
      return;
    }

    // 获取当前渲染目标
    const currentRenderTarget = this.renderer.getRenderTarget();

    // 设置反馈渲染目标
    this.renderer.setRenderTarget(this.virtualTexturing.getFeedbackTexture());

    // 渲染反馈场景
    this.renderer.render(this.feedbackScene, this.feedbackCamera);

    // 恢复渲染目标
    this.renderer.setRenderTarget(currentRenderTarget);

    // 读取反馈纹理
    this.virtualTexturing.readFeedbackTexture();
  }

  /**
   * 获取虚拟纹理
   * @returns 虚拟纹理
   */
  public getVirtualTexturing(): TerrainVirtualTexturing {
    return this.virtualTexturing;
  }

  /**
   * 获取物理纹理
   * @returns 物理纹理
   */
  public getPhysicalTexture(): THREE.Texture | null {
    return this.virtualTexturing.getPhysicalTexture();
  }

  /**
   * 注册事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public on(event: TerrainVirtualTexturingSystemEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public off(event: TerrainVirtualTexturingSystemEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }
}
