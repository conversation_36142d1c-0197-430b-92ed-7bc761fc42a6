/**
 * 水体渲染器
 * 用于渲染水体，包括反射、折射、波动等效果
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import type { Camera   } from '../Camera';
import { Scene } from '../../scene/Scene';
import { WaterBodyComponent, WaterBodyType } from '../../physics/water/WaterBodyComponent';
import { WaterMaterial } from './WaterMaterial';
import { Debug } from '../../utils/Debug';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';

/**
 * 水体渲染器配置
 */
export interface WaterRendererConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 是否启用反射 */
  enableReflection?: boolean;
  /** 是否启用折射 */
  enableRefraction?: boolean;
  /** 是否启用因果波纹 */
  enableCaustics?: boolean;
  /** 是否启用水下雾效 */
  enableUnderwaterFog?: boolean;
  /** 是否启用水下扭曲 */
  enableUnderwaterDistortion?: boolean;
  /** 是否启用深度测试 */
  enableDepthTest?: boolean;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
  /** 反射贴图分辨率 */
  reflectionMapResolution?: number;
  /** 折射贴图分辨率 */
  refractionMapResolution?: number;
  /** 是否使用延迟渲染 */
  useDeferredRendering?: boolean;
  /** 是否使用屏幕空间反射 */
  useScreenSpaceReflection?: boolean;
}

/**
 * 水体渲染器
 */
export class WaterRenderer extends System {
  /** 配置 */
  private config: WaterRendererConfig;
  /** 水体组件映射 */
  private waterBodies: Map<number, WaterBodyComponent> = new Map();
  /** 水体材质映射 */
  private waterMaterials: Map<number, WaterMaterial> = new Map();
  /** 反射相机 */
  private reflectionCamera: THREE.PerspectiveCamera | null = null;
  /** 折射相机 */
  private refractionCamera: THREE.PerspectiveCamera | null = null;
  /** 反射渲染目标 */
  private reflectionRenderTarget: THREE.WebGLRenderTarget | null = null;
  /** 折射渲染目标 */
  private refractionRenderTarget: THREE.WebGLRenderTarget | null = null;
  /** 深度渲染目标 */
  private depthRenderTarget: THREE.WebGLRenderTarget | null = null;
  /** 因果波纹渲染目标 */
  private causticsRenderTarget: THREE.WebGLRenderTarget | null = null;
  /** 帧计数器 */
  private frameCount: number = 0;
  /** 活跃相机 */
  private activeCamera: Camera | null = null;
  /** 活跃场景 */
  private activeScene: Scene | null = null;
  /** 渲染器 */
  private renderer: THREE.WebGLRenderer | null = null;
  /** 性能监视器 */
  private performanceMonitor: PerformanceMonitor = new PerformanceMonitor();
  /** 反射平面 */
  private reflectionPlane: THREE.Plane = new THREE.Plane();
  /** 折射平面 */
  private refractionPlane: THREE.Plane = new THREE.Plane();
  /** 临时矩阵 */
  private tempMatrix: THREE.Matrix4 = new THREE.Matrix4();

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config: WaterRendererConfig = {}) {
    super('WaterRenderer');
    
    // 设置配置
    this.config = {
      enabled: config.enabled !== undefined ? config.enabled : true,
      autoUpdate: config.autoUpdate !== undefined ? config.autoUpdate : true,
      updateFrequency: config.updateFrequency || 1,
      enableReflection: config.enableReflection !== undefined ? config.enableReflection : true,
      enableRefraction: config.enableRefraction !== undefined ? config.enableRefraction : true,
      enableCaustics: config.enableCaustics !== undefined ? config.enableCaustics : false,
      enableUnderwaterFog: config.enableUnderwaterFog !== undefined ? config.enableUnderwaterFog : true,
      enableUnderwaterDistortion: config.enableUnderwaterDistortion !== undefined ? config.enableUnderwaterDistortion : true,
      enableDepthTest: config.enableDepthTest !== undefined ? config.enableDepthTest : true,
      enablePerformanceMonitoring: config.enablePerformanceMonitoring !== undefined ? config.enablePerformanceMonitoring : false,
      reflectionMapResolution: config.reflectionMapResolution || 512,
      refractionMapResolution: config.refractionMapResolution || 512,
      useDeferredRendering: config.useDeferredRendering !== undefined ? config.useDeferredRendering : false,
      useScreenSpaceReflection: config.useScreenSpaceReflection !== undefined ? config.useScreenSpaceReflection : false
    };
    
    // 设置世界
    this.setWorld(world);
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    // 如果启用了性能监控，初始化性能监视器
    if (this.config.enablePerformanceMonitoring) {
      this.performanceMonitor.initialize();
    }
    
    // 创建反射和折射相机
    this.createCameras();
    
    // 创建渲染目标
    this.createRenderTargets();
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.config.enabled || !this.config.autoUpdate) {
      return;
    }
    
    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.config.updateFrequency !== 0) {
      return;
    }
    
    // 如果没有活跃相机或场景，尝试查找
    if (!this.activeCamera || !this.activeScene) {
      this.findActiveCamera();
      this.findActiveScene();
    }
    
    // 如果没有渲染器，尝试获取
    if (!this.renderer && this.world) {
      this.renderer = this.world.getRenderer();
    }
    
    // 如果缺少必要组件，则不更新
    if (!this.activeCamera || !this.activeScene || !this.renderer) {
      return;
    }
    
    // 如果启用了性能监控，开始计时
    if (this.config.enablePerformanceMonitoring) {
      this.performanceMonitor.start('waterRenderUpdate');
    }
    
    // 更新所有水体
    this.updateWaterBodies(deltaTime);
    
    // 如果启用了性能监控，结束计时
    if (this.config.enablePerformanceMonitoring) {
      this.performanceMonitor.end('waterRenderUpdate');
    }
  }

  /**
   * 添加水体组件
   * @param entity 实体
   * @param component 水体组件
   */
  public addWaterBody(entity: Entity, component: WaterBodyComponent): void {
    this.waterBodies.set(entity.id, component);
    
    // 创建水体材质
    const material = this.createWaterMaterial(component);
    this.waterMaterials.set(entity.id, material);
    
    Debug.log('WaterRenderer', `添加水体组件: ${entity.id}`);
  }

  /**
   * 移除水体组件
   * @param entity 实体
   */
  public removeWaterBody(entity: Entity): void {
    this.waterBodies.delete(entity.id);
    this.waterMaterials.delete(entity.id);
    
    Debug.log('WaterRenderer', `移除水体组件: ${entity.id}`);
  }

  // 其他方法将在后续实现
}
