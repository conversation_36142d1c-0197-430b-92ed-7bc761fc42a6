/**
 * 地形基准测试示例
 * 用于测试不同地形优化技术的性能效果
 */
import * as THREE from 'three';
import { PerformanceBenchmark, TestScene, OptimizationTechnique, BenchmarkEventType } from '../utils/PerformanceBenchmark';
import { TerrainGeometryCompression } from '../terrain/optimization/TerrainGeometryCompression';
import { TerrainNormalMapOptimizer } from '../terrain/optimization/TerrainNormalMapOptimizer';
import { TerrainVirtualTexturing } from '../terrain/textures/TerrainVirtualTexturing';
import { Debug } from '../utils/Debug';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';

/**
 * 地形基准测试示例
 */
export class TerrainBenchmarkExample {
  /** 渲染器 */
  private renderer: THREE.WebGLRenderer;
  /** 相机 */
  private camera: THREE.PerspectiveCamera;
  /** 控制器 */
  private controls: OrbitControls;
  /** 基准测试 */
  private benchmark: PerformanceBenchmark;
  /** 容器 */
  private container: HTMLElement | null;
  /** 是否运行中 */
  private running: boolean;
  /** 动画帧ID */
  private animationFrameId: number | null;
  /** 当前场景 */
  private currentScene: THREE.Scene | null;

  /**
   * 构造函数
   * @param container 容器
   */
  constructor(container: HTMLElement | null = null) {
    this.container = container;
    this.running = false;
    this.animationFrameId = null;
    this.currentScene = null;

    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

    if (container) {
      container.appendChild(this.renderer.domElement);
    } else {
      document.body.appendChild(this.renderer.domElement);
    }

    // 创建相机
    this.camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 10000);
    this.camera.setPosition(0, 100, 200);
    this.camera.lookAt(0, 0, 0);

    // 创建控制器
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.enableDamping = true;
    this.controls.dampingFactor = 0.05;
    this.controls.screenSpacePanning = false;
    this.controls.minDistance = 10;
    this.controls.maxDistance = 1000;
    this.controls.maxPolarAngle = Math.PI / 2;

    // 获取基准测试实例
    this.benchmark = PerformanceBenchmark.getInstance();
    this.benchmark.setRenderer(this.renderer);
    this.benchmark.setCamera(this.camera);

    // 配置基准测试
    this.benchmark.configure({
      enabled: true,
      testDuration: 5000,
      warmupDuration: 1000,
      sampleInterval: 100,
      autoRunAll: false,
      debug: true
    });

    // 注册测试场景
    this.registerTestScenes();

    // 注册优化技术
    this.registerOptimizationTechniques();

    // 添加事件监听器
    this.addEventListeners();

    // 处理窗口大小变化
    window.addEventListener('resize', this.onWindowResize.bind(this));
  }

  /**
   * 注册测试场景
   */
  private registerTestScenes(): void {
    // 注册基础地形场景
    const basicTerrainScene: TestScene = {
      id: 'basic_terrain',
      name: '基础地形',
      description: '未优化的基础地形场景',
      create: this.createBasicTerrainScene.bind(this),
      cleanup: this.cleanupScene.bind(this),
      update: this.updateScene.bind(this)
    };
    this.benchmark.registerScene(basicTerrainScene);

    // 注册复杂地形场景
    const complexTerrainScene: TestScene = {
      id: 'complex_terrain',
      name: '复杂地形',
      description: '包含更多细节的复杂地形场景',
      create: this.createComplexTerrainScene.bind(this),
      cleanup: this.cleanupScene.bind(this),
      update: this.updateScene.bind(this),
      config: {
        size: 1024,
        segments: 256,
        heightScale: 100,
        detailLevels: 4
      }
    };
    this.benchmark.registerScene(complexTerrainScene);
  }

  /**
   * 注册优化技术
   */
  private registerOptimizationTechniques(): void {
    // 注册无优化技术（基准）
    const noOptimizationTechnique: OptimizationTechnique = {
      id: 'no_optimization',
      name: '无优化',
      description: '不应用任何优化技术',
      apply: (_scene: THREE.Scene, _renderer: THREE.WebGLRenderer) => {
        // 不做任何优化
        Debug.log('TerrainBenchmarkExample', '应用无优化技术');
      },
      cleanup: (_scene: THREE.Scene, _renderer: THREE.WebGLRenderer) => {
        // 不需要清理
      }
    };
    this.benchmark.registerTechnique(noOptimizationTechnique);

    // 注册几何体压缩技术
    const geometryCompressionTechnique: OptimizationTechnique = {
      id: 'geometry_compression',
      name: '几何体压缩',
      description: '应用几何体压缩优化',
      apply: this.applyGeometryCompression.bind(this),
      cleanup: this.cleanupGeometryCompression.bind(this),
      config: {
        simplificationQuality: 0.5,
        useQuantization: true,
        positionQuantizationBits: 12,
        normalQuantizationBits: 8,
        uvQuantizationBits: 10
      }
    };
    this.benchmark.registerTechnique(geometryCompressionTechnique);

    // 注册法线贴图优化技术
    const normalMapOptimizationTechnique: OptimizationTechnique = {
      id: 'normal_map_optimization',
      name: '法线贴图优化',
      description: '应用法线贴图优化',
      apply: this.applyNormalMapOptimization.bind(this),
      cleanup: this.cleanupNormalMapOptimization.bind(this),
      config: {
        scaleFactor: 0.5,
        useCompression: true,
        compressionFormat: 'ETC1',
        compressionQuality: 0.8
      }
    };
    this.benchmark.registerTechnique(normalMapOptimizationTechnique);

    // 注册虚拟纹理技术
    const virtualTexturingTechnique: OptimizationTechnique = {
      id: 'virtual_texturing',
      name: '虚拟纹理',
      description: '应用虚拟纹理优化',
      apply: this.applyVirtualTexturing.bind(this),
      cleanup: this.cleanupVirtualTexturing.bind(this),
      config: {
        physicalTextureSize: 2048,
        pageSize: 256,
        pageBorderSize: 4,
        maxMipLevels: 8,
        useCompression: true
      }
    };
    this.benchmark.registerTechnique(virtualTexturingTechnique);

    // 注册组合优化技术
    const combinedOptimizationTechnique: OptimizationTechnique = {
      id: 'combined_optimization',
      name: '组合优化',
      description: '应用几何体压缩、法线贴图优化和虚拟纹理的组合优化',
      apply: this.applyCombinedOptimization.bind(this),
      cleanup: this.cleanupCombinedOptimization.bind(this),
      config: {
        // 组合所有优化的配置
        geometryCompression: {
          simplificationQuality: 0.5,
          useQuantization: true,
          positionQuantizationBits: 12,
          normalQuantizationBits: 8,
          uvQuantizationBits: 10
        },
        normalMapOptimization: {
          scaleFactor: 0.5,
          useCompression: true,
          compressionFormat: 'ETC1',
          compressionQuality: 0.8
        },
        virtualTexturing: {
          physicalTextureSize: 2048,
          pageSize: 256,
          pageBorderSize: 4,
          maxMipLevels: 8,
          useCompression: true
        }
      }
    };
    this.benchmark.registerTechnique(combinedOptimizationTechnique);
  }

  /**
   * 添加事件监听器
   */
  private addEventListeners(): void {
    // 测试开始事件
    this.benchmark.addEventListener(BenchmarkEventType.TEST_STARTED, (data: any) => {
      Debug.log('TerrainBenchmarkExample', `测试开始: ${data.testId}`);
    });

    // 测试进度事件
    this.benchmark.addEventListener(BenchmarkEventType.TEST_PROGRESS, (data: any) => {
      const progressPercent = Math.round(data.progress * 100);
      Debug.log('TerrainBenchmarkExample', `测试进度: ${data.phase} ${progressPercent}%`);
    });

    // 测试完成事件
    this.benchmark.addEventListener(BenchmarkEventType.TEST_COMPLETED, (result: any) => {
      Debug.log('TerrainBenchmarkExample', `测试完成: ${result.id}`);
      Debug.log('TerrainBenchmarkExample', `平均FPS: ${result.averageFps.toFixed(2)}`);
      Debug.log('TerrainBenchmarkExample', `平均渲染时间: ${result.averageRenderTime.toFixed(2)}ms`);
    });

    // 所有测试完成事件
    this.benchmark.addEventListener(BenchmarkEventType.ALL_TESTS_COMPLETED, (results: any[]) => {
      Debug.log('TerrainBenchmarkExample', `所有测试完成，共 ${results.length} 个测试`);

      // 比较结果
      if (results.length >= 2) {
        const baselineResult = results.find(r => r.techniqueId === 'no_optimization');
        if (baselineResult) {
          for (const result of results) {
            if (result.techniqueId !== 'no_optimization') {
              const comparison = this.benchmark.compareResults(baselineResult.id, result.id);
              if (comparison) {
                Debug.log('TerrainBenchmarkExample', `比较结果: ${result.techniqueId}`);
                Debug.log('TerrainBenchmarkExample', `FPS改进: ${comparison.fpsImprovement.toFixed(2)}%`);
                Debug.log('TerrainBenchmarkExample', `渲染时间改进: ${comparison.renderTimeImprovement.toFixed(2)}%`);
                Debug.log('TerrainBenchmarkExample', `总体改进: ${comparison.overallImprovement.toFixed(2)}%`);
              }
            }
          }
        }
      }
    });
  }

  /**
   * 窗口大小变化处理
   */
  private onWindowResize(): void {
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }

  /**
   * 创建基础地形场景
   * @returns 场景
   */
  private createBasicTerrainScene(): THREE.Scene {
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0x87ceeb);

    // 添加环境光
    const ambientLight = new THREE.AmbientLight(0x404040);
    scene.add(ambientLight);

    // 添加平行光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(1, 1, 1).normalize();
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 500;
    directionalLight.shadow.camera.left = -100;
    directionalLight.shadow.camera.right = 100;
    directionalLight.shadow.camera.top = 100;
    directionalLight.shadow.camera.bottom = -100;
    scene.add(directionalLight);

    // 创建地形
    const size = 512;
    const segments = 128;
    const geometry = new THREE.PlaneGeometry(size, size, segments, segments);

    // 生成高度图
    this.generateHeightMap(geometry, 50);

    // 计算法线
    geometry.computeVertexNormals();

    // 创建材质
    const material = new THREE.MeshStandardMaterial({
      color: 0x3d8c40,
      side: THREE.DoubleSide,
      flatShading: false,
      metalness: 0.0,
      roughness: 0.8
    });

    // 创建地形网格
    const terrain = new THREE.Mesh(geometry, material);
    terrain.rotation.x = -Math.PI / 2;
    terrain.receiveShadow = true;
    terrain.castShadow = true;
    terrain.name = 'terrain';
    scene.add(terrain);

    // 添加坐标轴辅助
    const axesHelper = new THREE.AxesHelper(50);
    scene.add(axesHelper);

    return scene;
  }

  /**
   * 创建复杂地形场景
   * @returns 场景
   */
  private createComplexTerrainScene(): THREE.Scene {
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0x87ceeb);

    // 添加环境光
    const ambientLight = new THREE.AmbientLight(0x404040);
    scene.add(ambientLight);

    // 添加平行光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(1, 1, 1).normalize();
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 4096;
    directionalLight.shadow.mapSize.height = 4096;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 1000;
    directionalLight.shadow.camera.left = -200;
    directionalLight.shadow.camera.right = 200;
    directionalLight.shadow.camera.top = 200;
    directionalLight.shadow.camera.bottom = -200;
    scene.add(directionalLight);

    // 使用默认配置
    const size = 1024;
    const segments = 256;
    const heightScale = 100;
    const detailLevels = 4;

    // 创建地形
    const geometry = new THREE.PlaneGeometry(size, size, segments, segments);

    // 生成复杂高度图
    this.generateComplexHeightMap(geometry, heightScale, detailLevels);

    // 计算法线
    geometry.computeVertexNormals();

    // 创建材质
    const material = new THREE.MeshStandardMaterial({
      color: 0x3d8c40,
      side: THREE.DoubleSide,
      flatShading: false,
      metalness: 0.0,
      roughness: 0.8,
      wireframe: false
    });

    // 创建地形网格
    const terrain = new THREE.Mesh(geometry, material);
    terrain.rotation.x = -Math.PI / 2;
    terrain.receiveShadow = true;
    terrain.castShadow = true;
    terrain.name = 'terrain';
    scene.add(terrain);

    // 添加坐标轴辅助
    const axesHelper = new THREE.AxesHelper(100);
    scene.add(axesHelper);

    // 添加雾效
    scene.fog = new THREE.FogExp2(0xaaaaaa, 0.0005);

    return scene;
  }

  /**
   * 生成高度图
   * @param geometry 几何体
   * @param heightScale 高度缩放
   */
  private generateHeightMap(geometry: THREE.PlaneGeometry, heightScale: number): void {
    const positions = geometry.attributes.position.array as Float32Array;

    for (let i = 0; i < positions.length; i += 3) {
      const x = positions[i];
      const z = positions[i + 2];

      // 使用柏林噪声生成高度
      const height = this.perlinNoise(x * 0.01, z * 0.01) * heightScale;
      positions[i + 1] = height;
    }

    geometry.attributes.position.needsUpdate = true;
  }

  /**
   * 生成复杂高度图
   * @param geometry 几何体
   * @param heightScale 高度缩放
   * @param detailLevels 细节级别
   */
  private generateComplexHeightMap(geometry: THREE.PlaneGeometry, heightScale: number, detailLevels: number): void {
    const positions = geometry.attributes.position.array as Float32Array;

    for (let i = 0; i < positions.length; i += 3) {
      const x = positions[i];
      const z = positions[i + 2];

      // 使用多层柏林噪声生成高度
      let height = 0;
      let amplitude = 1.0;
      let frequency = 0.005;

      for (let j = 0; j < detailLevels; j++) {
        height += this.perlinNoise(x * frequency, z * frequency) * amplitude;
        amplitude *= 0.5;
        frequency *= 2.0;
      }

      positions[i + 1] = height * heightScale;
    }

    geometry.attributes.position.needsUpdate = true;
  }

  /**
   * 柏林噪声
   * @param x X坐标
   * @param y Y坐标
   * @returns 噪声值
   */
  private perlinNoise(x: number, y: number): number {
    // 简化的柏林噪声实现
    const X = Math.floor(x) & 255;
    const Y = Math.floor(y) & 255;

    const xf = x - Math.floor(x);
    const yf = y - Math.floor(y);

    const topRight = this.randomGradient(X + 1, Y + 1);
    const topLeft = this.randomGradient(X, Y + 1);
    const bottomRight = this.randomGradient(X + 1, Y);
    const bottomLeft = this.randomGradient(X, Y);

    const tx1 = xf * xf * (3 - 2 * xf);
    const ty1 = yf * yf * (3 - 2 * yf);

    return this.lerp(
      this.lerp(bottomLeft, bottomRight, tx1),
      this.lerp(topLeft, topRight, tx1),
      ty1
    );
  }

  /**
   * 随机梯度
   * @param ix X索引
   * @param iy Y索引
   * @returns 随机值
   */
  private randomGradient(ix: number, iy: number): number {
    // 简化的随机梯度实现
    const w = 8 * 32;
    const s = w / 2;
    let a = ix, b = iy;
    a *= 3284157443; b ^= (a << s) | (a >>> (w - s));
    b *= 1911520717; a ^= (b << s) | (b >>> (w - s));
    a *= 2048419325;
    const random = a * (Math.PI / ~(~0 >>> 1));
    return Math.cos(random);
  }

  /**
   * 线性插值
   * @param a 值A
   * @param b 值B
   * @param t 插值因子
   * @returns 插值结果
   */
  private lerp(a: number, b: number, t: number): number {
    return a + t * (b - a);
  }

  /**
   * 更新场景
   * @param deltaTime 时间增量
   */
  private updateScene(_deltaTime: number): void {
    // 更新控制器
    this.controls.update();

    // 获取地形
    const terrain = this.currentScene?.getObjectByName('terrain') as THREE.Mesh;
    if (terrain) {
      // 可以在这里添加动画或其他更新
    }
  }

  /**
   * 清理场景
   */
  private cleanupScene(): void {
    if (this.currentScene) {
      // 释放几何体和材质
      this.currentScene.traverse((object) => {
        if (object instanceof THREE.Mesh) {
          if (object.geometry) {
            (object.geometry as any).dispose();
          }

          if (object.material) {
            if (Array.isArray(object.material)) {
              for (const material of object.material) {
                this.disposeMaterial(material);
              }
            } else {
              this.disposeMaterial(object.material);
            }
          }
        }
      });

      // 清空场景
      while (this.currentScene.children.length > 0) {
        this.currentScene.remove(this.currentScene.children[0]);
      }
    }
  }

  /**
   * 释放材质
   * @param material 材质
   */
  private disposeMaterial(material: THREE.Material): void {
    // 释放纹理
    for (const key in material) {
      const value = (material as any)[key];
      if (value && value.isTexture) {
        (value as any).dispose();
      }
    }

    // 释放材质
    (material as any).dispose();
  }

  /**
   * 应用几何体压缩
   * @param scene 场景
   * @param renderer 渲染器
   * @param config 配置
   */
  private applyGeometryCompression(scene: THREE.Scene, _renderer: THREE.WebGLRenderer, config?: any): void {
    Debug.log('TerrainBenchmarkExample', '应用几何体压缩优化');

    // 获取地形
    const terrain = scene.getObjectByName('terrain') as THREE.Mesh;
    if (!terrain || !terrain.geometry) {
      Debug.warn('TerrainBenchmarkExample', '未找到地形或几何体');
      return;
    }

    // 创建几何体压缩器
    const compressor = new TerrainGeometryCompression({
      simplificationQuality: config?.simplificationQuality || 0.5,
      useQuantization: config?.useQuantization || true,
      positionQuantizationBits: config?.positionQuantizationBits || 12,
      normalQuantizationBits: config?.normalQuantizationBits || 8,
      uvQuantizationBits: config?.uvQuantizationBits || 10
    });

    // 压缩几何体
    compressor.compressGeometry(terrain.geometry as THREE.BufferGeometry)
      .then((result) => {
        // 更新地形几何体
        (terrain.geometry as any).dispose();
        terrain.geometry = result.compressedGeometry;

        Debug.log('TerrainBenchmarkExample', `几何体压缩完成，压缩率: ${result.compressionRatio.toFixed(2)}x`);
      })
      .catch((error) => {
        Debug.error('TerrainBenchmarkExample', `几何体压缩失败: ${error}`);
      });
  }

  /**
   * 清理几何体压缩
   * @param scene 场景
   * @param renderer 渲染器
   */
  private cleanupGeometryCompression(_scene: THREE.Scene, _renderer: THREE.WebGLRenderer): void {
    // 不需要特殊清理
    Debug.log('TerrainBenchmarkExample', '清理几何体压缩优化');
  }

  /**
   * 应用法线贴图优化
   * @param scene 场景
   * @param renderer 渲染器
   * @param config 配置
   */
  private applyNormalMapOptimization(scene: THREE.Scene, _renderer: THREE.WebGLRenderer, config?: any): void {
    Debug.log('TerrainBenchmarkExample', '应用法线贴图优化');

    // 获取地形
    const terrain = scene.getObjectByName('terrain') as THREE.Mesh;
    if (!terrain || !terrain.geometry || !terrain.material) {
      Debug.warn('TerrainBenchmarkExample', '未找到地形、几何体或材质');
      return;
    }

    // 创建法线贴图优化器
    const optimizer = new TerrainNormalMapOptimizer({
      useCompression: config?.useCompression || true,
      compressionQuality: config?.compressionQuality || 0.8
    });

    // 生成法线贴图
    const material = terrain.material as THREE.MeshStandardMaterial;

    // 从高度图生成法线贴图
    this.generateHeightMapTexture(terrain.geometry as THREE.BufferGeometry)
      .then((heightMap) => {
        // 优化法线贴图
        optimizer.generateNormalMapFromHeightMap(heightMap, 1.0)
          .then((normalMap) => {
            // 优化法线贴图
            optimizer.optimizeNormalMap(normalMap)
              .then((result) => {
                // 更新材质
                material.normalMap = result.optimizedTexture;
                material.normalScale.set(1, 1);
                material.needsUpdate = true;

                Debug.log('TerrainBenchmarkExample', `法线贴图优化完成，压缩率: ${result.optimizationRatio.toFixed(2)}x`);
              })
              .catch((error) => {
                Debug.error('TerrainBenchmarkExample', `法线贴图优化失败: ${error}`);
              });
          });
      });
  }

  /**
   * 生成高度图纹理
   * @param geometry 几何体
   * @returns 高度图纹理
   */
  private async generateHeightMapTexture(geometry: THREE.BufferGeometry): Promise<THREE.Texture> {
    // 获取位置数据
    const positions = geometry.attributes.position.array;
    const width = Math.sqrt(positions.length / 3);

    // 创建画布
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = width;
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      throw new Error('无法创建2D上下文');
    }

    // 创建图像数据
    const imageData = ctx.createImageData(width, width);
    const data = imageData.data;

    // 找到最小和最大高度
    let minHeight = Infinity;
    let maxHeight = -Infinity;

    for (let i = 0; i < positions.length; i += 3) {
      const height = positions[i + 1];
      minHeight = Math.min(minHeight, height);
      maxHeight = Math.max(maxHeight, height);
    }

    const heightRange = maxHeight - minHeight;

    // 填充图像数据
    let index = 0;
    for (let i = 0; i < positions.length; i += 3) {
      const height = positions[i + 1];
      const normalizedHeight = (height - minHeight) / heightRange;
      const value = Math.floor(normalizedHeight * 255);

      data[index++] = value;     // R
      data[index++] = value;     // G
      data[index++] = value;     // B
      data[index++] = 255;       // A
    }

    // 更新画布
    ctx.putImageData(imageData, 0, 0);

    // 创建纹理
    const texture = new THREE.CanvasTexture(canvas);
    texture.wrapS = THREE.ClampToEdgeWrapping;
    texture.wrapT = THREE.ClampToEdgeWrapping;

    return texture;
  }

  /**
   * 清理法线贴图优化
   * @param scene 场景
   * @param renderer 渲染器
   */
  private cleanupNormalMapOptimization(scene: THREE.Scene, _renderer: THREE.WebGLRenderer): void {
    // 获取地形
    const terrain = scene.getObjectByName('terrain') as THREE.Mesh;
    if (terrain && terrain.material) {
      const material = terrain.material as THREE.MeshStandardMaterial;

      // 移除法线贴图
      if (material.normalMap) {
        (material.normalMap as any).dispose();
        material.normalMap = null;
        material.needsUpdate = true;
      }
    }

    Debug.log('TerrainBenchmarkExample', '清理法线贴图优化');
  }

  /**
   * 应用虚拟纹理
   * @param scene 场景
   * @param renderer 渲染器
   * @param config 配置
   */
  private applyVirtualTexturing(scene: THREE.Scene, renderer: THREE.WebGLRenderer, config?: any): void {
    Debug.log('TerrainBenchmarkExample', '应用虚拟纹理优化');

    // 获取地形
    const terrain = scene.getObjectByName('terrain') as THREE.Mesh;
    if (!terrain || !terrain.geometry || !terrain.material) {
      Debug.warn('TerrainBenchmarkExample', '未找到地形、几何体或材质');
      return;
    }

    // 创建虚拟纹理
    const virtualTexturing = new TerrainVirtualTexturing({
      physicalTextureSize: config?.physicalTextureSize || 2048,
      pageSize: config?.pageSize || 256,
      pageBorderSize: config?.pageBorderSize || 4,
      maxMipLevels: config?.maxMipLevels || 8,
      useCompression: config?.useCompression || true
    });

    // 设置渲染器
    virtualTexturing.setRenderer(renderer);

    // 生成高度图纹理
    this.generateHeightMapTexture(terrain.geometry as THREE.BufferGeometry)
      .then((heightMap) => {
        // 设置源纹理
        virtualTexturing.setSourceTexture(heightMap);

        // 创建虚拟纹理材质
        const material = terrain.material as THREE.MeshStandardMaterial;

        // 应用虚拟纹理到地形组件
        virtualTexturing.applyToTerrainComponent({
          entity: { id: 'terrain' },
          getMaterial: () => material
        } as any);

        // 存储虚拟纹理到场景用户数据
        scene.userData.virtualTexturing = virtualTexturing;

        Debug.log('TerrainBenchmarkExample', '虚拟纹理应用完成');
      });
  }

  /**
   * 清理虚拟纹理
   * @param scene 场景
   * @param renderer 渲染器
   */
  private cleanupVirtualTexturing(scene: THREE.Scene, _renderer: THREE.WebGLRenderer): void {
    // 获取虚拟纹理
    const virtualTexturing = scene.userData.virtualTexturing as TerrainVirtualTexturing;
    if (virtualTexturing) {
      // 释放资源
      (virtualTexturing as any).dispose();

      // 移除引用
      delete scene.userData.virtualTexturing;
    }

    // 获取地形
    const terrain = scene.getObjectByName('terrain') as THREE.Mesh;
    if (terrain && terrain.material) {
      const material = terrain.material as THREE.MeshStandardMaterial;

      // 重置材质
      material.needsUpdate = true;
    }

    Debug.log('TerrainBenchmarkExample', '清理虚拟纹理优化');
  }

  /**
   * 应用组合优化
   * @param scene 场景
   * @param renderer 渲染器
   * @param config 配置
   */
  private applyCombinedOptimization(scene: THREE.Scene, renderer: THREE.WebGLRenderer, config?: any): void {
    Debug.log('TerrainBenchmarkExample', '应用组合优化');

    // 应用几何体压缩
    this.applyGeometryCompression(scene, renderer, config?.geometryCompression);

    // 应用法线贴图优化
    this.applyNormalMapOptimization(scene, renderer, config?.normalMapOptimization);

    // 应用虚拟纹理
    this.applyVirtualTexturing(scene, renderer, config?.virtualTexturing);
  }

  /**
   * 清理组合优化
   * @param scene 场景
   * @param renderer 渲染器
   */
  private cleanupCombinedOptimization(scene: THREE.Scene, renderer: THREE.WebGLRenderer): void {
    // 清理虚拟纹理
    this.cleanupVirtualTexturing(scene, renderer);

    // 清理法线贴图优化
    this.cleanupNormalMapOptimization(scene, renderer);

    // 清理几何体压缩
    this.cleanupGeometryCompression(scene, renderer);

    Debug.log('TerrainBenchmarkExample', '清理组合优化');
  }

  /**
   * 启动基准测试
   */
  public start(): void {
    if (this.running) {
      Debug.warn('TerrainBenchmarkExample', '基准测试已在运行');
      return;
    }

    this.running = true;

    // 启动渲染循环
    this.startRenderLoop();

    Debug.log('TerrainBenchmarkExample', '基准测试启动');
  }

  /**
   * 停止基准测试
   */
  public stop(): void {
    if (!this.running) {
      return;
    }

    this.running = false;

    // 停止渲染循环
    if (this.animationFrameId !== null) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }

    Debug.log('TerrainBenchmarkExample', '基准测试停止');
  }

  /**
   * 启动渲染循环
   */
  private startRenderLoop(): void {
    const animate = () => {
      this.animationFrameId = requestAnimationFrame(animate);

      if (this.controls) {
        this.controls.update();
      }

      if (this.renderer && this.camera && this.currentScene) {
        this.renderer.render(this.currentScene, this.camera);
      }
    };

    animate();
  }

  /**
   * 运行单个测试
   * @param sceneId 场景ID
   * @param techniqueId 技术ID
   */
  public runTest(sceneId: string, techniqueId: string): void {
    this.benchmark.runTest(sceneId, techniqueId);
  }

  /**
   * 运行所有测试
   */
  public runAllTests(): void {
    this.benchmark.runAllTests();
  }

  /**
   * 获取当前场景
   * @returns 当前场景
   */
  public getCurrentScene(): THREE.Scene | null {
    return this.currentScene;
  }

  /**
   * 设置当前场景
   * @param scene 场景
   */
  public setCurrentScene(scene: THREE.Scene): void {
    this.currentScene = scene;
  }
}
