/**
 * 水体天气影响系统
 * 实现雨滴、风等天气对水面的影响
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import type { Camera   } from '../../rendering/Camera';
import { WaterBodyComponent } from './WaterBodyComponent';
import { Debug } from '../../utils/Debug';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';
import { EventEmitter } from '../../utils/EventEmitter';
import { WaterInstancedRenderer, WaterEffectType } from '../../rendering/water/WaterInstancedRenderer';

/**
 * 天气类型
 */
export enum WeatherType {
  /** 晴天 */
  SUNNY = 'sunny',
  /** 多云 */
  CLOUDY = 'cloudy',
  /** 雨天 */
  RAINY = 'rainy',
  /** 暴雨 */
  HEAVY_RAIN = 'heavyRain',
  /** 雷雨 */
  THUNDERSTORM = 'thunderstorm',
  /** 大风 */
  WINDY = 'windy',
  /** 暴风 */
  STORM = 'storm',
  /** 雪天 */
  SNOWY = 'snowy',
  /** 大雪 */
  HEAVY_SNOW = 'heavySnow',
  /** 雾天 */
  FOGGY = 'foggy'
}

/**
 * 水体天气影响配置
 */
export interface WaterWeatherSystemConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 天气类型 */
  weatherType?: WeatherType;
  /** 风速 */
  windSpeed?: number;
  /** 风向 */
  windDirection?: number;
  /** 雨强度 */
  rainIntensity?: number;
  /** 雪强度 */
  snowIntensity?: number;
  /** 雾强度 */
  fogIntensity?: number;
  /** 雷电强度 */
  thunderIntensity?: number;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
}

/**
 * 水体天气影响系统事件类型
 */
export enum WaterWeatherSystemEventType {
  /** 天气更新 */
  WEATHER_UPDATED = 'weatherUpdated',
  /** 天气参数变化 */
  WEATHER_PARAMS_CHANGED = 'weatherParamsChanged',
  /** 雨滴生成 */
  RAINDROP_GENERATED = 'raindropGenerated',
  /** 雪花生成 */
  SNOWFLAKE_GENERATED = 'snowflakeGenerated',
  /** 雷电生成 */
  THUNDER_GENERATED = 'thunderGenerated'
}

/**
 * 水体天气影响系统
 */
export class WaterWeatherSystem extends System {
  /** 系统类型 */
  public static readonly TYPE = 'WaterWeatherSystem';

  /** 配置 */
  private config: Required<WaterWeatherSystemConfig>;
  /** 水体实体映射 */
  private waterEntities: Map<Entity, WaterBodyComponent> = new Map();
  /** 帧计数器 */
  private frameCount: number = 0;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 性能监视器 */
  private performanceMonitor: PerformanceMonitor = new PerformanceMonitor();
  /** 调试对象 */
  private debugObjects: THREE.Object3D[] = [];
  /** 水体实例化渲染器 */
  private waterInstancedRenderer: WaterInstancedRenderer | null = null;
  /** 雨滴生成计时器 */
  private raindropTimer: number = 0;
  /** 雪花生成计时器 */
  private snowflakeTimer: number = 0;
  /** 雷电生成计时器 */
  private thunderTimer: number = 0;
  /** 风向向量 */
  private windVector: THREE.Vector2 = new THREE.Vector2();
  /** 临时向量 */
  private tempVector: THREE.Vector3 = new THREE.Vector3();
  /** 临时颜色 */
  private tempColor: THREE.Color = new THREE.Color();
  /** 临时欧拉角 */
  private tempEuler: THREE.Euler = new THREE.Euler();

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config: WaterWeatherSystemConfig = {}) {
    super(world);

    // 设置默认配置
    this.config = {
      enabled: config.enabled !== undefined ? config.enabled : true,
      autoUpdate: config.autoUpdate !== undefined ? config.autoUpdate : true,
      updateFrequency: config.updateFrequency || 1,
      weatherType: config.weatherType || WeatherType.SUNNY,
      windSpeed: config.windSpeed || 0,
      windDirection: config.windDirection || 0,
      rainIntensity: config.rainIntensity || 0,
      snowIntensity: config.snowIntensity || 0,
      fogIntensity: config.fogIntensity || 0,
      thunderIntensity: config.thunderIntensity || 0,
      useDebugVisualization: config.useDebugVisualization !== undefined ? config.useDebugVisualization : false
    };

    // 更新风向向量
    this.updateWindVector();

    // 初始化调试可视化
    if (this.config.useDebugVisualization) {
      this.initializeDebugVisualization();
    }
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    super.initialize();
    Debug.log('WaterWeatherSystem', '水体天气影响系统初始化');

    // 获取水体实例化渲染器
    this.waterInstancedRenderer = this.world.getSystem('WaterInstancedRenderer') as WaterInstancedRenderer;
    if (!this.waterInstancedRenderer) {
      Debug.warn('WaterWeatherSystem', '未找到水体实例化渲染器');
    }
  }

  /**
   * 初始化调试可视化
   */
  private initializeDebugVisualization(): void {
    // 创建调试容器
    const debugContainer = new THREE.Object3D();
    debugContainer.name = 'WaterWeatherDebug';
    this.world.getScene()?.add(debugContainer);

    // 创建风向指示器
    const windArrow = new THREE.ArrowHelper(
      new THREE.Vector3(Math.cos(this.config.windDirection), 0, Math.sin(this.config.windDirection)),
      new THREE.Vector3(0, 10, 0),
      5,
      0x00ff00
    );
    debugContainer.add(windArrow);
    this.debugObjects.push(windArrow);

    // 创建雨强度指示器
    const rainIndicator = new THREE.Mesh(
      new THREE.SphereGeometry(1, 8, 8),
      new THREE.MeshBasicMaterial({ color: 0x0000ff })
    );
    rainIndicator.position.set(5, 10, 0);
    rainIndicator.scale.set(this.config.rainIntensity, this.config.rainIntensity, this.config.rainIntensity);
    debugContainer.add(rainIndicator);
    this.debugObjects.push(rainIndicator);
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.enabled || !this.config.autoUpdate) {
      return;
    }

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.config.updateFrequency !== 0) {
      return;
    }

    // 如果启用了性能监控，开始计时
    if (this.config.useDebugVisualization) {
      this.performanceMonitor.start('waterWeatherUpdate');
    }

    // 更新天气效果
    this.updateWeatherEffects(deltaTime);

    // 更新调试可视化
    if (this.config.useDebugVisualization) {
      this.updateDebugVisualization();
      this.performanceMonitor.end('waterWeatherUpdate');
    }
  }

  /**
   * 更新天气效果
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateWeatherEffects(deltaTime: number): void {
    // 根据天气类型更新效果
    switch (this.config.weatherType) {
      case WeatherType.RAINY:
      case WeatherType.HEAVY_RAIN:
      case WeatherType.THUNDERSTORM:
        this.updateRainEffects(deltaTime);
        break;
      case WeatherType.SNOWY:
      case WeatherType.HEAVY_SNOW:
        this.updateSnowEffects(deltaTime);
        break;
      case WeatherType.WINDY:
      case WeatherType.STORM:
        this.updateWindEffects(deltaTime);
        break;
      case WeatherType.FOGGY:
        this.updateFogEffects(deltaTime);
        break;
    }

    // 如果是雷雨，更新雷电效果
    if (this.config.weatherType === WeatherType.THUNDERSTORM) {
      this.updateThunderEffects(deltaTime);
    }

    // 更新水体材质
    this.updateWaterMaterials(deltaTime);
  }

  /**
   * 更新雨效果
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateRainEffects(deltaTime: number): void {
    // 更新雨滴生成计时器
    this.raindropTimer += deltaTime;

    // 计算雨滴生成间隔
    const raindropInterval = this.getRaindropInterval();

    // 如果达到生成间隔，生成雨滴
    if (this.raindropTimer >= raindropInterval) {
      this.raindropTimer = 0;
      this.generateRaindrops();
    }
  }

  /**
   * 获取雨滴生成间隔
   * @returns 雨滴生成间隔（秒）
   */
  private getRaindropInterval(): number {
    // 根据雨强度计算生成间隔
    switch (this.config.weatherType) {
      case WeatherType.RAINY:
        return 0.1 / this.config.rainIntensity;
      case WeatherType.HEAVY_RAIN:
        return 0.05 / this.config.rainIntensity;
      case WeatherType.THUNDERSTORM:
        return 0.03 / this.config.rainIntensity;
      default:
        return 1.0;
    }
  }

  /**
   * 生成雨滴
   */
  private generateRaindrops(): void {
    // 如果没有水体实例化渲染器，返回
    if (!this.waterInstancedRenderer) {
      return;
    }

    // 获取相机
    const camera = this.getCamera();
    if (!camera) {
      return;
    }

    // 获取相机位置
    const cameraPosition = camera.getThreeCamera().position;

    // 遍历所有水体
    for (const [entity, component] of this.waterEntities.entries()) {
      // 获取水体包围盒
      const boundingBox = component.getBoundingBox();
      if (!boundingBox) {
        continue;
      }

      // 获取水面高度
      const waterHeight = component.getPosition().y;

      // 计算水体中心
      const center = new THREE.Vector3();
      boundingBox.getCenter(center);
      center.y = waterHeight;

      // 计算水体尺寸
      const size = new THREE.Vector3();
      boundingBox.getSize(size);

      // 计算相机到水体中心的距离
      const distance = cameraPosition.distanceTo(center);

      // 如果距离太远，跳过
      if (distance > 500) {
        continue;
      }

      // 计算雨滴数量
      const raindropCount = this.calculateRaindropCount();

      // 生成雨滴
      for (let i = 0; i < raindropCount; i++) {
        // 计算随机位置
        const x = center.x + (Math.random() - 0.5) * size.x * 0.8;
        const z = center.z + (Math.random() - 0.5) * size.z * 0.8;

        // 创建雨滴位置
        const position = new THREE.Vector3(x, waterHeight, z);

        // 创建雨滴
        this.createRaindrop(position);
      }
    }
  }

  /**
   * 计算雨滴数量
   * @returns 雨滴数量
   */
  private calculateRaindropCount(): number {
    // 根据雨强度计算雨滴数量
    switch (this.config.weatherType) {
      case WeatherType.RAINY:
        return Math.floor(5 * this.config.rainIntensity);
      case WeatherType.HEAVY_RAIN:
        return Math.floor(10 * this.config.rainIntensity);
      case WeatherType.THUNDERSTORM:
        return Math.floor(15 * this.config.rainIntensity);
      default:
        return 0;
    }
  }

  /**
   * 创建雨滴
   * @param position 位置
   */
  private createRaindrop(position: THREE.Vector3): void {
    // 如果没有水体实例化渲染器，返回
    if (!this.waterInstancedRenderer) {
      return;
    }

    // 创建雨滴特效
    const id = this.waterInstancedRenderer.createEffectInstance(
      WaterEffectType.SPLASH,
      position,
      {
        rotation: new THREE.Euler(0, Math.random() * Math.PI * 2, 0),
        scale: new THREE.Vector3(
          0.5 + Math.random() * 0.5,
          0.5 + Math.random() * 0.5,
          0.5 + Math.random() * 0.5
        ),
        color: new THREE.Color(0x88ccff),
        opacity: 0.8,
        lifetime: 0.5 + Math.random() * 0.5,
        userData: {
          velocityX: this.windVector.x * this.config.windSpeed * 0.1,
          velocityZ: this.windVector.y * this.config.windSpeed * 0.1
        }
      }
    );

    // 创建波纹特效
    this.waterInstancedRenderer.createEffectInstance(
      WaterEffectType.RIPPLE,
      position,
      {
        rotation: new THREE.Euler(-Math.PI / 2, 0, 0),
        scale: new THREE.Vector3(
          0.5 + Math.random() * 0.5,
          0.5 + Math.random() * 0.5,
          1
        ),
        color: new THREE.Color(0xffffff),
        opacity: 0.5,
        lifetime: 1.0 + Math.random() * 0.5
      }
    );

    // 发出事件
    this.eventEmitter.emit(WaterWeatherSystemEventType.RAINDROP_GENERATED, {
      position,
      id
    });
  }

  /**
   * 更新雪效果
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateSnowEffects(deltaTime: number): void {
    // 更新雪花生成计时器
    this.snowflakeTimer += deltaTime;

    // 计算雪花生成间隔
    const snowflakeInterval = this.getSnowflakeInterval();

    // 如果达到生成间隔，生成雪花
    if (this.snowflakeTimer >= snowflakeInterval) {
      this.snowflakeTimer = 0;
      this.generateSnowflakes();
    }
  }

  /**
   * 获取雪花生成间隔
   * @returns 雪花生成间隔（秒）
   */
  private getSnowflakeInterval(): number {
    // 根据雪强度计算生成间隔
    switch (this.config.weatherType) {
      case WeatherType.SNOWY:
        return 0.2 / this.config.snowIntensity;
      case WeatherType.HEAVY_SNOW:
        return 0.1 / this.config.snowIntensity;
      default:
        return 1.0;
    }
  }

  /**
   * 生成雪花
   */
  private generateSnowflakes(): void {
    // 如果没有水体实例化渲染器，返回
    if (!this.waterInstancedRenderer) {
      return;
    }

    // 获取相机
    const camera = this.getCamera();
    if (!camera) {
      return;
    }

    // 获取相机位置
    const cameraPosition = camera.getThreeCamera().position;

    // 遍历所有水体
    for (const [entity, component] of this.waterEntities.entries()) {
      // 获取水体包围盒
      const boundingBox = component.getBoundingBox();
      if (!boundingBox) {
        continue;
      }

      // 获取水面高度
      const waterHeight = component.getPosition().y;

      // 计算水体中心
      const center = new THREE.Vector3();
      boundingBox.getCenter(center);
      center.y = waterHeight;

      // 计算水体尺寸
      const size = new THREE.Vector3();
      boundingBox.getSize(size);

      // 计算相机到水体中心的距离
      const distance = cameraPosition.distanceTo(center);

      // 如果距离太远，跳过
      if (distance > 500) {
        continue;
      }

      // 计算雪花数量
      const snowflakeCount = this.calculateSnowflakeCount();

      // 生成雪花
      for (let i = 0; i < snowflakeCount; i++) {
        // 计算随机位置
        const x = center.x + (Math.random() - 0.5) * size.x * 0.8;
        const z = center.z + (Math.random() - 0.5) * size.z * 0.8;

        // 创建雪花位置
        const position = new THREE.Vector3(x, waterHeight, z);

        // 创建雪花
        this.createSnowflake(position);
      }
    }
  }

  /**
   * 计算雪花数量
   * @returns 雪花数量
   */
  private calculateSnowflakeCount(): number {
    // 根据雪强度计算雪花数量
    switch (this.config.weatherType) {
      case WeatherType.SNOWY:
        return Math.floor(3 * this.config.snowIntensity);
      case WeatherType.HEAVY_SNOW:
        return Math.floor(8 * this.config.snowIntensity);
      default:
        return 0;
    }
  }

  /**
   * 创建雪花
   * @param position 位置
   */
  private createSnowflake(position: THREE.Vector3): void {
    // 如果没有水体实例化渲染器，返回
    if (!this.waterInstancedRenderer) {
      return;
    }

    // 创建雪花特效
    const id = this.waterInstancedRenderer.createEffectInstance(
      WaterEffectType.DROPLET,
      position,
      {
        rotation: new THREE.Euler(0, Math.random() * Math.PI * 2, 0),
        scale: new THREE.Vector3(
          0.3 + Math.random() * 0.3,
          0.3 + Math.random() * 0.3,
          0.3 + Math.random() * 0.3
        ),
        color: new THREE.Color(0xffffff),
        opacity: 0.9,
        lifetime: 0.8 + Math.random() * 0.5,
        userData: {
          velocityX: this.windVector.x * this.config.windSpeed * 0.05,
          velocityY: -0.5,
          velocityZ: this.windVector.y * this.config.windSpeed * 0.05
        }
      }
    );

    // 创建波纹特效
    this.waterInstancedRenderer.createEffectInstance(
      WaterEffectType.RIPPLE,
      position,
      {
        rotation: new THREE.Euler(-Math.PI / 2, 0, 0),
        scale: new THREE.Vector3(
          0.3 + Math.random() * 0.3,
          0.3 + Math.random() * 0.3,
          1
        ),
        color: new THREE.Color(0xffffff),
        opacity: 0.3,
        lifetime: 0.8 + Math.random() * 0.3
      }
    );

    // 发出事件
    this.eventEmitter.emit(WaterWeatherSystemEventType.SNOWFLAKE_GENERATED, {
      position,
      id
    });
  }

  /**
   * 更新风效果
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateWindEffects(deltaTime: number): void {
    // 更新所有水体的材质
    for (const [entity, component] of this.waterEntities.entries()) {
      // 获取水体材质
      const material = component.getMaterial();
      if (!material) {
        continue;
      }

      // 更新风向和风速
      if (material instanceof THREE.ShaderMaterial) {
        const uniforms = material.uniforms;
        if (uniforms.windDirection) {
          uniforms.windDirection.value = this.config.windDirection;
        }
        if (uniforms.windSpeed) {
          uniforms.windSpeed.value = this.config.windSpeed;
        }
      }
    }

    // 如果风速很大，创建水雾
    if (this.config.windSpeed > 5) {
      this.generateWaterMist(deltaTime);
    }
  }

  /**
   * 生成水雾
   * @param deltaTime 帧间隔时间（秒）
   */
  private generateWaterMist(deltaTime: number): void {
    // 如果没有水体实例化渲染器，返回
    if (!this.waterInstancedRenderer) {
      return;
    }

    // 获取相机
    const camera = this.getCamera();
    if (!camera) {
      return;
    }

    // 获取相机位置
    const cameraPosition = camera.getThreeCamera().position;

    // 遍历所有水体
    for (const [entity, component] of this.waterEntities.entries()) {
      // 获取水体包围盒
      const boundingBox = component.getBoundingBox();
      if (!boundingBox) {
        continue;
      }

      // 获取水面高度
      const waterHeight = component.getPosition().y;

      // 计算水体中心
      const center = new THREE.Vector3();
      boundingBox.getCenter(center);
      center.y = waterHeight;

      // 计算水体尺寸
      const size = new THREE.Vector3();
      boundingBox.getSize(size);

      // 计算相机到水体中心的距离
      const distance = cameraPosition.distanceTo(center);

      // 如果距离太远，跳过
      if (distance > 500) {
        continue;
      }

      // 计算水雾生成概率
      const mistProbability = this.config.windSpeed / 20;

      // 根据概率生成水雾
      if (Math.random() < mistProbability) {
        // 计算随机位置
        const x = center.x + (Math.random() - 0.5) * size.x * 0.9;
        const z = center.z + (Math.random() - 0.5) * size.z * 0.9;

        // 创建水雾位置
        const position = new THREE.Vector3(x, waterHeight + 0.1, z);

        // 创建水雾
        this.createWaterMist(position);
      }
    }
  }

  /**
   * 创建水雾
   * @param position 位置
   */
  private createWaterMist(position: THREE.Vector3): void {
    // 如果没有水体实例化渲染器，返回
    if (!this.waterInstancedRenderer) {
      return;
    }

    // 创建水雾特效
    const id = this.waterInstancedRenderer.createEffectInstance(
      WaterEffectType.MIST,
      position,
      {
        rotation: new THREE.Euler(-Math.PI / 2, 0, Math.random() * Math.PI * 2),
        scale: new THREE.Vector3(
          1 + Math.random() * 2,
          1 + Math.random() * 2,
          1
        ),
        color: new THREE.Color(0xffffff),
        opacity: 0.2 + Math.random() * 0.1,
        lifetime: 2.0 + Math.random() * 1.0,
        userData: {
          windX: this.windVector.x * this.config.windSpeed * 0.2,
          windZ: this.windVector.y * this.config.windSpeed * 0.2
        }
      }
    );
  }

  /**
   * 更新雾效果
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateFogEffects(deltaTime: number): void {
    // 更新场景雾
    const scene = this.world.getScene();
    if (!scene) {
      return;
    }

    // 如果场景没有雾，创建雾
    if (!scene.fog) {
      scene.fog = new THREE.FogExp2(0x88ccff, 0.01 * this.config.fogIntensity);
    } else if (scene.fog instanceof THREE.FogExp2) {
      // 更新雾密度
      scene.fog.density = 0.01 * this.config.fogIntensity;
    }
  }

  /**
   * 更新雷电效果
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateThunderEffects(deltaTime: number): void {
    // 更新雷电生成计时器
    this.thunderTimer += deltaTime;

    // 计算雷电生成间隔
    const thunderInterval = 5.0 / this.config.thunderIntensity;

    // 如果达到生成间隔，生成雷电
    if (this.thunderTimer >= thunderInterval) {
      this.thunderTimer = 0;
      this.generateThunder();
    }
  }

  /**
   * 生成雷电
   */
  private generateThunder(): void {
    // 获取场景
    const scene = this.world.getScene();
    if (!scene) {
      return;
    }

    // 创建闪电光源
    const light = new THREE.PointLight(0xffffff, 1, 1000);

    // 随机位置
    light.position.set(
      Math.random() * 1000 - 500,
      100,
      Math.random() * 1000 - 500
    );

    // 添加到场景
    scene.add(light);

    // 闪烁效果
    const duration = 0.1 + Math.random() * 0.2;
    let elapsed = 0;

    // 创建动画
    const animate = () => {
      requestAnimationFrame(animate);
      elapsed += 0.016;

      // 闪烁强度
      light.intensity = Math.max(0, 1 - elapsed / duration) * 5;

      // 结束动画
      if (elapsed >= duration) {
        scene.remove(light);
        return;
      }
    };

    // 开始动画
    animate();

    // 发出事件
    this.eventEmitter.emit(WaterWeatherSystemEventType.THUNDER_GENERATED, {
      position: light.position,
      duration
    });
  }

  /**
   * 更新水体材质
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateWaterMaterials(deltaTime: number): void {
    // 遍历所有水体
    for (const [entity, component] of this.waterEntities.entries()) {
      // 获取水体材质
      const material = component.getMaterial();
      if (!material) {
        continue;
      }

      // 更新材质参数
      if (material instanceof THREE.ShaderMaterial) {
        const uniforms = material.uniforms;

        // 更新风向和风速
        if (uniforms.windDirection) {
          uniforms.windDirection.value = this.config.windDirection;
        }
        if (uniforms.windSpeed) {
          uniforms.windSpeed.value = this.config.windSpeed;
        }

        // 更新波浪参数
        if (uniforms.waveStrength) {
          // 根据风速调整波浪强度
          const waveStrength = 1.0 + this.config.windSpeed * 0.1;
          uniforms.waveStrength.value = waveStrength;
        }
      }
    }
  }

  /**
   * 更新风向向量
   */
  private updateWindVector(): void {
    this.windVector.set(
      Math.cos(this.config.windDirection),
      Math.sin(this.config.windDirection)
    );
  }

  /**
   * 更新调试可视化
   */
  private updateDebugVisualization(): void {
    // 更新风向指示器
    if (this.debugObjects.length > 0) {
      const windArrow = this.debugObjects[0] as THREE.ArrowHelper;
      windArrow.setDirection(new THREE.Vector3(
        Math.cos(this.config.windDirection),
        0,
        Math.sin(this.config.windDirection)
      ));
      windArrow.setLength(this.config.windSpeed);
    }

    // 更新雨强度指示器
    if (this.debugObjects.length > 1) {
      const rainIndicator = this.debugObjects[1] as THREE.Mesh;
      rainIndicator.scale.set(
        this.config.rainIntensity,
        this.config.rainIntensity,
        this.config.rainIntensity
      );
    }
  }

  /**
   * 获取相机
   * @returns 相机
   */
  private getCamera(): Camera | null {
    // 获取相机系统
    const cameraSystem = this.world.getSystem('CameraSystem');
    if (!cameraSystem) {
      return null;
    }

    // 获取活动相机
    return (cameraSystem as any).getActiveCamera();
  }

  /**
   * 添加水体实体
   * @param entity 实体
   * @param component 水体组件
   */
  public addWaterEntity(entity: Entity, component: WaterBodyComponent): void {
    this.waterEntities.set(entity, component);
  }

  /**
   * 移除水体实体
   * @param entity 实体
   */
  public removeWaterEntity(entity: Entity): void {
    this.waterEntities.delete(entity);
  }

  /**
   * 设置天气类型
   * @param weatherType 天气类型
   */
  public setWeatherType(weatherType: WeatherType): void {
    this.config.weatherType = weatherType;

    // 根据天气类型设置默认参数
    switch (weatherType) {
      case WeatherType.SUNNY:
        this.config.windSpeed = 1;
        this.config.rainIntensity = 0;
        this.config.snowIntensity = 0;
        this.config.fogIntensity = 0;
        this.config.thunderIntensity = 0;
        break;
      case WeatherType.CLOUDY:
        this.config.windSpeed = 2;
        this.config.rainIntensity = 0;
        this.config.snowIntensity = 0;
        this.config.fogIntensity = 0.2;
        this.config.thunderIntensity = 0;
        break;
      case WeatherType.RAINY:
        this.config.windSpeed = 3;
        this.config.rainIntensity = 1;
        this.config.snowIntensity = 0;
        this.config.fogIntensity = 0.3;
        this.config.thunderIntensity = 0;
        break;
      case WeatherType.HEAVY_RAIN:
        this.config.windSpeed = 5;
        this.config.rainIntensity = 2;
        this.config.snowIntensity = 0;
        this.config.fogIntensity = 0.5;
        this.config.thunderIntensity = 0;
        break;
      case WeatherType.THUNDERSTORM:
        this.config.windSpeed = 8;
        this.config.rainIntensity = 3;
        this.config.snowIntensity = 0;
        this.config.fogIntensity = 0.7;
        this.config.thunderIntensity = 1;
        break;
      case WeatherType.WINDY:
        this.config.windSpeed = 10;
        this.config.rainIntensity = 0;
        this.config.snowIntensity = 0;
        this.config.fogIntensity = 0.1;
        this.config.thunderIntensity = 0;
        break;
      case WeatherType.STORM:
        this.config.windSpeed = 15;
        this.config.rainIntensity = 2;
        this.config.snowIntensity = 0;
        this.config.fogIntensity = 0.6;
        this.config.thunderIntensity = 0.5;
        break;
      case WeatherType.SNOWY:
        this.config.windSpeed = 2;
        this.config.rainIntensity = 0;
        this.config.snowIntensity = 1;
        this.config.fogIntensity = 0.4;
        this.config.thunderIntensity = 0;
        break;
      case WeatherType.HEAVY_SNOW:
        this.config.windSpeed = 4;
        this.config.rainIntensity = 0;
        this.config.snowIntensity = 2;
        this.config.fogIntensity = 0.6;
        this.config.thunderIntensity = 0;
        break;
      case WeatherType.FOGGY:
        this.config.windSpeed = 1;
        this.config.rainIntensity = 0;
        this.config.snowIntensity = 0;
        this.config.fogIntensity = 1;
        this.config.thunderIntensity = 0;
        break;
    }

    // 更新风向向量
    this.updateWindVector();

    // 发出事件
    this.eventEmitter.emit(WaterWeatherSystemEventType.WEATHER_PARAMS_CHANGED, this.config);
  }

  /**
   * 设置天气参数
   * @param params 参数
   */
  public setWeatherParams(params: Partial<WaterWeatherSystemConfig>): void {
    // 更新配置
    if (params.weatherType !== undefined) {
      this.config.weatherType = params.weatherType;
    }
    if (params.windSpeed !== undefined) {
      this.config.windSpeed = params.windSpeed;
    }
    if (params.windDirection !== undefined) {
      this.config.windDirection = params.windDirection;
      this.updateWindVector();
    }
    if (params.rainIntensity !== undefined) {
      this.config.rainIntensity = params.rainIntensity;
    }
    if (params.snowIntensity !== undefined) {
      this.config.snowIntensity = params.snowIntensity;
    }
    if (params.fogIntensity !== undefined) {
      this.config.fogIntensity = params.fogIntensity;
    }
    if (params.thunderIntensity !== undefined) {
      this.config.thunderIntensity = params.thunderIntensity;
    }

    // 发出事件
    this.eventEmitter.emit(WaterWeatherSystemEventType.WEATHER_PARAMS_CHANGED, this.config);
  }
}
