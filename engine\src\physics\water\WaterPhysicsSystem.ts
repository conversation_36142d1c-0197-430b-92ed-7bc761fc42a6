/**
 * 水体物理系统
 * 用于模拟水体物理行为，包括水流动态、浮力和阻力等
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { PhysicsSystem } from '../PhysicsSystem';
import { WaterBodyComponent } from './WaterBodyComponent';
import { Debug } from '../../utils/Debug';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';
import { EventEmitter } from '../../utils/EventEmitter';
import { WorkerPool } from '../../utils/WorkerPool';
import {
  createWaterPhysicsWorker,
  WaterPhysicsWorkerMessageType,
  WaterWavesUpdateParams,
  WaterFlowUpdateParams,
  NormalMapUpdateParams
} from './WaterPhysicsWorker';

/**
 * 水体物理系统配置
 */
export interface WaterPhysicsSystemConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 是否启用浮力 */
  enableBuoyancy?: boolean;
  /** 是否启用阻力 */
  enableDrag?: boolean;
  /** 是否启用水流 */
  enableFlow?: boolean;
  /** 是否启用波动 */
  enableWaves?: boolean;
  /** 是否启用水体与物体碰撞 */
  enableCollision?: boolean;
  /** 是否启用水体粒子效果 */
  enableParticles?: boolean;
  /** 是否启用调试可视化 */
  enableDebugVisualization?: boolean;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
  /** 是否启用空间分区 */
  enableSpatialPartitioning?: boolean;
  /** 空间分区网格大小 */
  spatialGridSize?: number;
  /** 是否启用自适应更新频率 */
  enableAdaptiveUpdate?: boolean;
  /** 最小更新频率 */
  minUpdateFrequency?: number;
  /** 最大更新频率 */
  maxUpdateFrequency?: number;
  /** 是否启用多线程计算 */
  enableMultithreading?: boolean;
  /** 工作线程数量 */
  workerCount?: number;
  /** 是否启用水流冲击力 */
  enableFlowImpact?: boolean;
  /** 是否启用水体分裂 */
  enableWaterSplitting?: boolean;
}

/**
 * 水体物理系统事件类型
 */
export enum WaterPhysicsSystemEventType {
  /** 物体进入水体 */
  OBJECT_ENTER_WATER = 'object_enter_water',
  /** 物体离开水体 */
  OBJECT_EXIT_WATER = 'object_exit_water',
  /** 水体波动 */
  WATER_WAVE = 'water_wave',
  /** 水体流动 */
  WATER_FLOW = 'water_flow',
  /** 水体碰撞 */
  WATER_COLLISION = 'water_collision',
  /** 水流冲击 */
  WATER_FLOW_IMPACT = 'water_flow_impact',
  /** 水体分裂 */
  WATER_SPLITTING = 'water_splitting'
}

/**
 * 水体物理系统
 */
export class WaterPhysicsSystem extends System {
  /** 配置 */
  private config: WaterPhysicsSystemConfig;
  /** 物理系统 */
  private physicsSystem: PhysicsSystem | null = null;
  /** 水体组件映射 */
  private waterBodies: Map<number, WaterBodyComponent> = new Map();
  /** 帧计数器 */
  private frameCount: number = 0;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 性能监视器 */
  private performanceMonitor: PerformanceMonitor = new PerformanceMonitor();
  /** 调试渲染器 */
  private debugRenderer: THREE.Object3D | null = null;
  /** 空间分区网格 */
  private spatialGrid: Map<string, WaterBodyComponent[]> = new Map();
  /** 上一帧的性能数据 */
  private lastFramePerformance: { updateTime: number, bodyCount: number } = { updateTime: 0, bodyCount: 0 };
  /** 当前更新频率 */
  private currentUpdateFrequency: number = 1;
  /** 工作线程池 */
  private workerPool: WorkerPool | null = null;
  /** 是否支持Web Worker */
  private supportsWorkers: boolean = false;

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config: WaterPhysicsSystemConfig = {}) {
    super('WaterPhysicsSystem');

    // 设置配置
    this.config = {
      enabled: config.enabled !== undefined ? config.enabled : true,
      autoUpdate: config.autoUpdate !== undefined ? config.autoUpdate : true,
      updateFrequency: config.updateFrequency || 1,
      enableBuoyancy: config.enableBuoyancy !== undefined ? config.enableBuoyancy : true,
      enableDrag: config.enableDrag !== undefined ? config.enableDrag : true,
      enableFlow: config.enableFlow !== undefined ? config.enableFlow : true,
      enableWaves: config.enableWaves !== undefined ? config.enableWaves : true,
      enableCollision: config.enableCollision !== undefined ? config.enableCollision : true,
      enableParticles: config.enableParticles !== undefined ? config.enableParticles : false,
      enableDebugVisualization: config.enableDebugVisualization !== undefined ? config.enableDebugVisualization : false,
      enablePerformanceMonitoring: config.enablePerformanceMonitoring !== undefined ? config.enablePerformanceMonitoring : false,
      enableSpatialPartitioning: config.enableSpatialPartitioning !== undefined ? config.enableSpatialPartitioning : true,
      spatialGridSize: config.spatialGridSize || 10,
      enableAdaptiveUpdate: config.enableAdaptiveUpdate !== undefined ? config.enableAdaptiveUpdate : true,
      minUpdateFrequency: config.minUpdateFrequency || 1,
      maxUpdateFrequency: config.maxUpdateFrequency || 10,
      enableMultithreading: config.enableMultithreading !== undefined ? config.enableMultithreading : false,
      enableFlowImpact: config.enableFlowImpact !== undefined ? config.enableFlowImpact : false,
      enableWaterSplitting: config.enableWaterSplitting !== undefined ? config.enableWaterSplitting : false
    };

    // 设置当前更新频率
    this.currentUpdateFrequency = this.config.updateFrequency || 1;

    // 设置世界
    this.setWorld(world);

    // 获取物理系统
    if (world) {
      this.physicsSystem = world.getSystem('PhysicsSystem') as PhysicsSystem;
    }

    // 创建调试渲染器
    if (this.config.enableDebugVisualization) {
      this.createDebugRenderer();
    }

    // 初始化多线程
    if (this.config.enableMultithreading) {
      this.initializeWorker();
    }
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    // 如果没有物理系统，尝试获取
    if (!this.physicsSystem && this.world) {
      this.physicsSystem = this.world.getSystem('PhysicsSystem') as PhysicsSystem;
    }

    // 如果启用了性能监控，初始化性能监视器
    if (this.config.enablePerformanceMonitoring) {
      this.performanceMonitor.initialize();
    }

    // 如果启用了空间分区，初始化空间网格
    if (this.config.enableSpatialPartitioning) {
      this.initializeSpatialGrid();
    }
  }

  /**
   * 初始化工作线程池
   */
  private initializeWorker(): void {
    try {
      // 检查是否支持Web Worker
      this.supportsWorkers = typeof Worker !== 'undefined';

      if (!this.supportsWorkers) {
        Debug.warn('WaterPhysicsSystem', '当前环境不支持Web Worker，将使用主线程计算');
        this.config.enableMultithreading = false;
        return;
      }

      // 创建工作线程池
      const workerCount = this.config.workerCount || navigator.hardwareConcurrency || 4;
      this.workerPool = new WorkerPool(workerCount);

      // 设置工作线程创建器
      this.workerPool.setWorkerCreator(() => createWaterPhysicsWorker());

      Debug.log('WaterPhysicsSystem', `多线程初始化成功，创建了 ${workerCount} 个工作线程`);
    } catch (error) {
      Debug.error('WaterPhysicsSystem', `初始化工作线程池失败: ${error}`);
      this.config.enableMultithreading = false;
      this.supportsWorkers = false;
    }
  }

  /**
   * 初始化空间网格
   */
  private initializeSpatialGrid(): void {
    // 清空现有网格
    this.spatialGrid.clear();

    // 遍历所有水体组件
    for (const [entityId, waterBody] of this.waterBodies.entries()) {
      // 获取水体位置
      const position = waterBody.getPosition();
      const size = waterBody.getSize();

      // 计算网格坐标
      const gridCoords = this.getGridCoordinates(position, size);

      // 添加到网格
      for (const coord of gridCoords) {
        if (!this.spatialGrid.has(coord)) {
          this.spatialGrid.set(coord, []);
        }
        this.spatialGrid.get(coord)!.push(waterBody);
      }
    }

    Debug.log('WaterPhysicsSystem', `空间网格初始化完成，共${this.spatialGrid.size}个网格单元`);
  }

  /**
   * 获取网格坐标
   * @param position 位置
   * @param size 大小
   * @returns 网格坐标数组
   */
  private getGridCoordinates(position: THREE.Vector3, size: { width: number, height: number, depth: number }): string[] {
    const gridSize = this.config.spatialGridSize || 10;
    const coords: string[] = [];

    // 计算水体覆盖的网格范围
    const minX = Math.floor((position.x - size.width / 2) / gridSize);
    const maxX = Math.floor((position.x + size.width / 2) / gridSize);
    const minZ = Math.floor((position.z - size.depth / 2) / gridSize);
    const maxZ = Math.floor((position.z + size.depth / 2) / gridSize);

    // 生成所有覆盖的网格坐标
    for (let x = minX; x <= maxX; x++) {
      for (let z = minZ; z <= maxZ; z++) {
        coords.push(`${x},${z}`);
      }
    }

    return coords;
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public async update(deltaTime: number): Promise<void> {
    if (!this.config.enabled || !this.config.autoUpdate) {
      return;
    }

    // 如果启用了自适应更新频率，调整更新频率
    if (this.config.enableAdaptiveUpdate) {
      this.adjustUpdateFrequency();
    }

    // 按照当前更新频率更新
    this.frameCount++;
    if (this.frameCount % this.currentUpdateFrequency !== 0) {
      return;
    }

    // 如果启用了性能监控，开始计时
    if (this.config.enablePerformanceMonitoring) {
      this.performanceMonitor.start('waterPhysicsUpdate');
    }

    // 更新所有水体
    if (this.config.enableSpatialPartitioning) {
      await this.updateWaterBodiesWithSpatialPartitioning(deltaTime);
    } else {
      await this.updateWaterBodies(deltaTime);
    }

    // 如果启用了调试可视化，更新调试渲染器
    if (this.config.enableDebugVisualization && this.debugRenderer) {
      this.updateDebugRenderer();
    }

    // 如果启用了性能监控，结束计时并记录性能数据
    if (this.config.enablePerformanceMonitoring) {
      const updateTime = this.performanceMonitor.end('waterPhysicsUpdate');
      this.lastFramePerformance = {
        updateTime,
        bodyCount: this.waterBodies.size
      };
    }
  }

  /**
   * 调整更新频率
   */
  private adjustUpdateFrequency(): void {
    // 如果没有性能数据，使用默认更新频率
    if (!this.lastFramePerformance.updateTime) {
      this.currentUpdateFrequency = this.config.updateFrequency || 1;
      return;
    }

    // 根据上一帧的性能数据调整更新频率
    const updateTime = this.lastFramePerformance.updateTime;
    const targetTime = 1 / 60; // 目标帧时间（16.67ms）

    if (updateTime > targetTime * 2) {
      // 性能不佳，降低更新频率
      this.currentUpdateFrequency = Math.min(
        this.currentUpdateFrequency + 1,
        this.config.maxUpdateFrequency || 10
      );
    } else if (updateTime < targetTime / 2) {
      // 性能良好，提高更新频率
      this.currentUpdateFrequency = Math.max(
        this.currentUpdateFrequency - 1,
        this.config.minUpdateFrequency || 1
      );
    }

    // 确保更新频率在有效范围内
    this.currentUpdateFrequency = Math.max(
      Math.min(this.currentUpdateFrequency, this.config.maxUpdateFrequency || 10),
      this.config.minUpdateFrequency || 1
    );
  }

  /**
   * 添加水体组件
   * @param entity 实体
   * @param component 水体组件
   */
  public addWaterBody(entity: Entity, component: WaterBodyComponent): void {
    this.waterBodies.set(entity.id, component);

    // 初始化水体组件
    component.initialize();

    // 如果启用了空间分区，更新空间网格
    if (this.config.enableSpatialPartitioning) {
      this.addWaterBodyToSpatialGrid(entity.id, component);
    }

    Debug.log('WaterPhysicsSystem', `添加水体组件: ${entity.id}`);
  }

  /**
   * 移除水体组件
   * @param entity 实体
   */
  public removeWaterBody(entity: Entity): void {
    // 如果启用了空间分区，从空间网格中移除
    if (this.config.enableSpatialPartitioning) {
      this.removeWaterBodyFromSpatialGrid(entity.id);
    }

    this.waterBodies.delete(entity.id);

    Debug.log('WaterPhysicsSystem', `移除水体组件: ${entity.id}`);
  }

  /**
   * 添加水体到空间网格
   * @param entityId 实体ID
   * @param waterBody 水体组件
   */
  private addWaterBodyToSpatialGrid(entityId: number, waterBody: WaterBodyComponent): void {
    // 获取水体位置
    const position = waterBody.getPosition();
    const size = waterBody.getSize();

    // 计算网格坐标
    const gridCoords = this.getGridCoordinates(position, size);

    // 添加到网格
    for (const coord of gridCoords) {
      if (!this.spatialGrid.has(coord)) {
        this.spatialGrid.set(coord, []);
      }
      this.spatialGrid.get(coord)!.push(waterBody);
    }
  }

  /**
   * 从空间网格中移除水体
   * @param entityId 实体ID
   */
  private removeWaterBodyFromSpatialGrid(entityId: number): void {
    // 遍历所有网格单元
    for (const [coord, waterBodies] of this.spatialGrid.entries()) {
      // 过滤掉要移除的水体
      const filteredWaterBodies = waterBodies.filter(wb => {
        // 获取水体所属的实体ID
        const wbEntityId = this.getEntityIdByWaterBody(wb);
        return wbEntityId !== entityId;
      });

      // 更新网格单元
      if (filteredWaterBodies.length === 0) {
        this.spatialGrid.delete(coord);
      } else {
        this.spatialGrid.set(coord, filteredWaterBodies);
      }
    }
  }

  /**
   * 根据水体组件获取实体ID
   * @param waterBody 水体组件
   * @returns 实体ID
   */
  private getEntityIdByWaterBody(waterBody: WaterBodyComponent): number {
    for (const [entityId, wb] of this.waterBodies.entries()) {
      if (wb === waterBody) {
        return entityId;
      }
    }
    return -1;
  }

  /**
   * 更新所有水体
   * @param deltaTime 帧间隔时间（秒）
   */
  private async updateWaterBodies(deltaTime: number): Promise<void> {
    // 创建更新任务数组
    const updateTasks: Promise<void>[] = [];

    // 遍历所有水体组件
    for (const [entityId, waterBody] of this.waterBodies.entries()) {
      // 添加更新任务
      updateTasks.push(this.updateWaterBody(waterBody, deltaTime));
    }

    // 等待所有更新任务完成
    await Promise.all(updateTasks);
  }

  /**
   * 使用空间分区更新水体
   * @param deltaTime 帧间隔时间（秒）
   */
  private async updateWaterBodiesWithSpatialPartitioning(deltaTime: number): Promise<void> {
    // 如果没有活跃相机，使用常规更新
    if (!this.world || !this.world.getActiveCamera()) {
      await this.updateWaterBodies(deltaTime);
      return;
    }

    // 获取相机位置
    const camera = this.world.getActiveCamera();
    const cameraPosition = camera.getPosition();

    // 计算相机所在的网格坐标
    const cameraGridX = Math.floor(cameraPosition.x / (this.config.spatialGridSize || 10));
    const cameraGridZ = Math.floor(cameraPosition.z / (this.config.spatialGridSize || 10));

    // 获取视锥体
    const frustum = camera.getFrustum();

    // 创建已更新水体的集合
    const updatedWaterBodies = new Set<WaterBodyComponent>();

    // 创建更新任务数组
    const updateTasks: Promise<void>[] = [];

    // 更新相机附近的网格
    const updateRadius = 3; // 更新相机周围3x3的网格
    for (let x = cameraGridX - updateRadius; x <= cameraGridX + updateRadius; x++) {
      for (let z = cameraGridZ - updateRadius; z <= cameraGridZ + updateRadius; z++) {
        const coord = `${x},${z}`;
        const waterBodies = this.spatialGrid.get(coord);

        if (waterBodies) {
          for (const waterBody of waterBodies) {
            // 避免重复更新
            if (!updatedWaterBodies.has(waterBody)) {
              // 检查水体是否在视锥体内
              const position = waterBody.getPosition();
              const size = waterBody.getSize();
              const boundingSphere = new THREE.Sphere(
                position,
                Math.max(size.width, size.depth) / 2
              );

              if (frustum.intersectsSphere(boundingSphere)) {
                // 添加更新任务
                updateTasks.push(this.updateWaterBody(waterBody, deltaTime));
                updatedWaterBodies.add(waterBody);
              }
            }
          }
        }
      }
    }

    // 对于远离相机的水体，使用简化更新
    for (const [entityId, waterBody] of this.waterBodies.entries()) {
      if (!updatedWaterBodies.has(waterBody)) {
        // 简化更新，只更新波动
        if (this.config.enableWaves) {
          updateTasks.push(this.updateWaterWaves(waterBody, deltaTime));
        }
      }
    }

    // 等待所有更新任务完成
    await Promise.all(updateTasks);
  }

  /**
   * 更新水体
   * @param waterBody 水体组件
   * @param deltaTime 帧间隔时间（秒）
   */
  private async updateWaterBody(waterBody: WaterBodyComponent, deltaTime: number): Promise<void> {
    // 如果水体未初始化或未启用，则跳过
    if (!waterBody.isInitialized() || !waterBody.isEnabled()) {
      return;
    }

    // 更新水体波动
    if (this.config.enableWaves) {
      await this.updateWaterWaves(waterBody, deltaTime);
    }

    // 更新水流
    if (this.config.enableFlow) {
      await this.updateWaterFlow(waterBody, deltaTime);
    }

    // 更新浮力和阻力
    if (this.config.enableBuoyancy || this.config.enableDrag) {
      this.updateBuoyancyAndDrag(waterBody, deltaTime);
    }

    // 更新碰撞
    if (this.config.enableCollision) {
      this.updateWaterCollision(waterBody, deltaTime);
    }

    // 更新粒子效果
    if (this.config.enableParticles) {
      this.updateWaterParticles(waterBody, deltaTime);
    }

    // 更新分裂效果
    if (this.config.enableWaterSplitting) {
      waterBody.updateSplittingEffects(deltaTime);
    }
  }

  /**
   * 更新水体波动
   * @param waterBody 水体组件
   * @param deltaTime 帧间隔时间（秒）
   */
  private async updateWaterWaves(waterBody: WaterBodyComponent, deltaTime: number): Promise<void> {
    // 获取波动参数
    const waveParams = waterBody.getWaveParams();
    if (!waveParams) return;

    // 更新波动时间
    waterBody.updateWaveTime(deltaTime * waveParams.speed);

    // 更新高度图
    await this.updateHeightMap(waterBody, deltaTime);

    // 更新法线图
    await this.updateNormalMap(waterBody);

    // 更新水体网格
    waterBody.updateWaterMesh();
  }

  /**
   * 更新高度图
   * @param waterBody 水体组件
   * @param deltaTime 帧间隔时间（秒）
   */
  private async updateHeightMap(waterBody: WaterBodyComponent, deltaTime: number): Promise<void> {
    const heightMap = waterBody.getHeightMap();
    if (!heightMap) return;

    const waveParams = waterBody.getWaveParams();
    if (!waveParams) return;

    const size = waterBody.getSize();
    const resolution = waterBody.getResolution();
    const time = waterBody.getWaveTime();

    // 如果启用了多线程且工作线程池可用，使用工作线程计算
    if (this.config.enableMultithreading && this.supportsWorkers && this.workerPool) {
      try {
        // 准备参数
        const params: WaterWavesUpdateParams = {
          heightMap: heightMap.slice(), // 创建副本以避免共享内存问题
          resolution,
          waveParams,
          time,
          deltaTime
        };

        // 发送到工作线程
        const response = await this.workerPool.postMessage(
          {
            type: WaterPhysicsWorkerMessageType.UPDATE_WAVES,
            data: params
          },
          [params.heightMap.buffer] // 转移高度图缓冲区以提高性能
        );

        // 处理结果
        if (response.data && response.data.type === WaterPhysicsWorkerMessageType.COMPLETE) {
          // 更新水体的高度图
          waterBody.setHeightMap(response.data.data.heightMap);
        } else if (response.data && response.data.type === WaterPhysicsWorkerMessageType.ERROR) {
          Debug.error('WaterPhysicsSystem', `更新水波失败: ${response.data.data.message}`);
          // 回退到主线程计算
          this.updateHeightMapInMainThread(waterBody, heightMap, waveParams, size, resolution, time);
        }
      } catch (error) {
        Debug.error('WaterPhysicsSystem', `使用工作线程更新水波失败: ${error}`);
        // 回退到主线程计算
        this.updateHeightMapInMainThread(waterBody, heightMap, waveParams, size, resolution, time);
      }
    } else {
      // 在主线程中计算
      this.updateHeightMapInMainThread(waterBody, heightMap, waveParams, size, resolution, time);
    }
  }

  /**
   * 在主线程中更新高度图
   * @param waterBody 水体组件
   * @param heightMap 高度图
   * @param waveParams 波动参数
   * @param size 尺寸
   * @param resolution 分辨率
   * @param time 时间
   */
  private updateHeightMapInMainThread(
    waterBody: WaterBodyComponent,
    heightMap: Float32Array,
    waveParams: any,
    size: any,
    resolution: number,
    time: number
  ): void {
    // 更新高度图
    for (let z = 0; z < resolution; z++) {
      for (let x = 0; x < resolution; x++) {
        const index = z * resolution + x;

        // 计算世界坐标
        const wx = (x / resolution - 0.5) * size.width;
        const wz = (z / resolution - 0.5) * size.depth;

        // 计算波动高度
        let height = 0;

        // 添加正弦波
        height += Math.sin(wx * waveParams.frequency + time) *
                  Math.cos(wz * waveParams.frequency + time) *
                  waveParams.amplitude;

        // 添加额外的波动
        height += Math.sin(wx * waveParams.frequency * 2.3 + time * 0.7) *
                  Math.cos(wz * waveParams.frequency * 1.8 + time * 0.9) *
                  waveParams.amplitude * 0.6;

        // 更新高度图
        heightMap[index] = height;
      }
    }

    // 更新水体的高度图
    waterBody.setHeightMap(heightMap);
  }

  /**
   * 更新法线图
   * @param waterBody 水体组件
   */
  private async updateNormalMap(waterBody: WaterBodyComponent): Promise<void> {
    const heightMap = waterBody.getHeightMap();
    const normalMap = waterBody.getNormalMap();
    if (!heightMap || !normalMap) return;

    const resolution = waterBody.getResolution();

    // 如果启用了多线程且工作线程池可用，使用工作线程计算
    if (this.config.enableMultithreading && this.supportsWorkers && this.workerPool) {
      try {
        // 准备参数
        const params: NormalMapUpdateParams = {
          heightMap: heightMap.slice(), // 创建副本以避免共享内存问题
          normalMap: normalMap.slice(), // 创建副本以避免共享内存问题
          resolution
        };

        // 发送到工作线程
        const response = await this.workerPool.postMessage(
          {
            type: WaterPhysicsWorkerMessageType.UPDATE_NORMAL_MAP,
            data: params
          },
          [params.heightMap.buffer, params.normalMap.buffer] // 转移缓冲区以提高性能
        );

        // 处理结果
        if (response.data && response.data.type === WaterPhysicsWorkerMessageType.COMPLETE) {
          // 更新水体的法线图
          waterBody.setNormalMap(response.data.data.normalMap);
        } else if (response.data && response.data.type === WaterPhysicsWorkerMessageType.ERROR) {
          Debug.error('WaterPhysicsSystem', `更新法线图失败: ${response.data.data.message}`);
          // 回退到主线程计算
          this.updateNormalMapInMainThread(waterBody, heightMap, normalMap, resolution);
        }
      } catch (error) {
        Debug.error('WaterPhysicsSystem', `使用工作线程更新法线图失败: ${error}`);
        // 回退到主线程计算
        this.updateNormalMapInMainThread(waterBody, heightMap, normalMap, resolution);
      }
    } else {
      // 在主线程中计算
      this.updateNormalMapInMainThread(waterBody, heightMap, normalMap, resolution);
    }
  }

  /**
   * 在主线程中更新法线图
   * @param waterBody 水体组件
   * @param heightMap 高度图
   * @param normalMap 法线图
   * @param resolution 分辨率
   */
  private updateNormalMapInMainThread(
    waterBody: WaterBodyComponent,
    heightMap: Float32Array,
    normalMap: Float32Array,
    resolution: number
  ): void {
    // 计算法线
    for (let z = 0; z < resolution; z++) {
      for (let x = 0; x < resolution; x++) {
        const index = (z * resolution + x) * 3;

        // 获取相邻点的高度
        const left = x > 0 ? heightMap[z * resolution + (x - 1)] : heightMap[z * resolution + x];
        const right = x < resolution - 1 ? heightMap[z * resolution + (x + 1)] : heightMap[z * resolution + x];
        const top = z > 0 ? heightMap[(z - 1) * resolution + x] : heightMap[z * resolution + x];
        const bottom = z < resolution - 1 ? heightMap[(z + 1) * resolution + x] : heightMap[z * resolution + x];

        // 计算法线
        const normal = new THREE.Vector3(
          left - right,
          2.0,
          top - bottom
        ).normalize();

        // 更新法线图
        normalMap[index] = normal.x;
        normalMap[index + 1] = normal.y;
        normalMap[index + 2] = normal.z;
      }
    }

    // 更新水体的法线图
    waterBody.setNormalMap(normalMap);
  }

  /**
   * 更新水流
   * @param waterBody 水体组件
   * @param deltaTime 帧间隔时间（秒）
   */
  private async updateWaterFlow(waterBody: WaterBodyComponent, deltaTime: number): Promise<void> {
    const heightMap = waterBody.getHeightMap();
    const velocityMap = waterBody.getVelocityMap();
    if (!velocityMap || !heightMap) return;

    const flowDirection = waterBody.getFlowDirection();
    const flowSpeed = waterBody.getFlowSpeed();
    const resolution = waterBody.getResolution();

    // 如果启用了多线程且工作线程池可用，使用工作线程计算
    if (this.config.enableMultithreading && this.supportsWorkers && this.workerPool) {
      try {
        // 准备参数
        const params: WaterFlowUpdateParams = {
          heightMap: heightMap.slice(), // 创建副本以避免共享内存问题
          velocityMap: velocityMap.slice(), // 创建副本以避免共享内存问题
          resolution,
          flowDirection,
          flowSpeed,
          deltaTime
        };

        // 发送到工作线程
        const response = await this.workerPool.postMessage(
          {
            type: WaterPhysicsWorkerMessageType.UPDATE_FLOW,
            data: params
          },
          [params.heightMap.buffer, params.velocityMap.buffer] // 转移缓冲区以提高性能
        );

        // 处理结果
        if (response.data && response.data.type === WaterPhysicsWorkerMessageType.COMPLETE) {
          // 更新水体的高度图和速度图
          waterBody.setHeightMap(response.data.data.heightMap);
          waterBody.setVelocityMap(response.data.data.velocityMap);
        } else if (response.data && response.data.type === WaterPhysicsWorkerMessageType.ERROR) {
          Debug.error('WaterPhysicsSystem', `更新水流失败: ${response.data.data.message}`);
          // 回退到主线程计算
          this.updateWaterFlowInMainThread(waterBody, velocityMap, flowDirection, flowSpeed, resolution);
        }
      } catch (error) {
        Debug.error('WaterPhysicsSystem', `使用工作线程更新水流失败: ${error}`);
        // 回退到主线程计算
        this.updateWaterFlowInMainThread(waterBody, velocityMap, flowDirection, flowSpeed, resolution);
      }
    } else {
      // 在主线程中计算
      this.updateWaterFlowInMainThread(waterBody, velocityMap, flowDirection, flowSpeed, resolution);
    }
  }

  /**
   * 在主线程中更新水流
   * @param waterBody 水体组件
   * @param velocityMap 速度图
   * @param flowDirection 流向
   * @param flowSpeed 流速
   * @param resolution 分辨率
   */
  private updateWaterFlowInMainThread(
    waterBody: WaterBodyComponent,
    velocityMap: Float32Array,
    flowDirection: any,
    flowSpeed: number,
    resolution: number
  ): void {
    // 更新速度图
    for (let z = 0; z < resolution; z++) {
      for (let x = 0; x < resolution; x++) {
        const index = (z * resolution + x) * 2;

        // 更新速度
        velocityMap[index] = flowDirection.x * flowSpeed;
        velocityMap[index + 1] = flowDirection.z * flowSpeed;
      }
    }

    // 更新水体的速度图
    waterBody.setVelocityMap(velocityMap);
  }
  /**
   * 更新浮力和阻力
   * @param waterBody 水体组件
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateBuoyancyAndDrag(waterBody: WaterBodyComponent, deltaTime: number): void {
    // 获取物理系统
    const physicsSystem = this.world.getSystem('PhysicsSystem');
    if (!physicsSystem) return;

    // 获取水体属性
    const density = waterBody.getDensity();
    const viscosity = waterBody.getViscosity();
    const position = waterBody.getPosition();
    const size = waterBody.getSize();
    const heightMap = waterBody.getHeightMap();
    const resolution = waterBody.getResolution();
    const velocityMap = waterBody.getVelocityMap();
    const flowSpeed = waterBody.getFlowSpeed();
    const flowDirection = waterBody.getFlowDirection();

    // 获取所有物理体
    const bodies = physicsSystem.getBodies();
    if (!bodies || bodies.size === 0) return;

    // 遍历所有物理体
    for (const [entityId, physicsBody] of bodies) {
      // 获取物理体位置和尺寸
      const bodyPosition = physicsBody.getPosition();
      const bodySize = physicsBody.getSize();
      const bodyMass = physicsBody.getMass();

      // 如果物理体质量为0（静态物体），则跳过
      if (bodyMass === 0) continue;

      // 检查物体是否在水体范围内
      const isInWaterX = bodyPosition.x >= position.x - size.width / 2 && bodyPosition.x <= position.x + size.width / 2;
      const isInWaterZ = bodyPosition.z >= position.z - size.depth / 2 && bodyPosition.z <= position.z + size.depth / 2;

      if (!isInWaterX || !isInWaterZ) continue;

      // 计算物体在水体中的位置
      const relativeX = (bodyPosition.x - (position.x - size.width / 2)) / size.width;
      const relativeZ = (bodyPosition.z - (position.z - size.depth / 2)) / size.depth;

      // 计算水面高度
      let waterHeight = position.y;

      // 如果有高度图，使用高度图计算水面高度
      if (heightMap && resolution > 0) {
        const gridX = Math.floor(relativeX * resolution);
        const gridZ = Math.floor(relativeZ * resolution);

        if (gridX >= 0 && gridX < resolution && gridZ >= 0 && gridZ < resolution) {
          const heightIndex = gridZ * resolution + gridX;
          waterHeight += heightMap[heightIndex];
        }
      }

      // 计算物体浸入水中的深度
      const submergedDepth = Math.max(0, waterHeight - (bodyPosition.y - bodySize.y / 2));
      const submergedRatio = Math.min(1, submergedDepth / bodySize.y);

      // 如果物体没有浸入水中，则跳过
      if (submergedRatio <= 0) continue;

      // 计算浮力
      if (waterBody.isBuoyancyEnabled()) {
        // 浮力 = 密度 * 重力 * 浸入体积
        const gravity = physicsSystem.getGravity();
        const submergedVolume = bodySize.x * bodySize.y * bodySize.z * submergedRatio;
        const buoyancyForce = new THREE.Vector3(0, -gravity.y * density * submergedVolume, 0);

        // 应用浮力
        physicsBody.applyForce(buoyancyForce);
      }

      // 计算阻力
      if (waterBody.isDragEnabled()) {
        // 获取物体速度
        const velocity = physicsBody.getVelocity();

        // 计算阻力
        const dragForce = new THREE.Vector3(
          -velocity.x * viscosity * submergedRatio,
          -velocity.y * viscosity * submergedRatio,
          -velocity.z * viscosity * submergedRatio
        );

        // 应用阻力
        physicsBody.applyForce(dragForce);
      }

      // 计算水流冲击力
      if (this.config.enableFlowImpact && flowSpeed > 0) {
        // 计算水流速度
        let flowVelocity = new THREE.Vector3(
          flowDirection.x * flowSpeed,
          0,
          flowDirection.z * flowSpeed
        );

        // 如果有速度图，使用速度图中的速度
        if (velocityMap && resolution > 0) {
          const gridX = Math.floor(relativeX * resolution);
          const gridZ = Math.floor(relativeZ * resolution);

          if (gridX >= 0 && gridX < resolution && gridZ >= 0 && gridZ < resolution) {
            const index = (gridZ * resolution + gridX) * 2;
            flowVelocity.x = velocityMap[index];
            flowVelocity.z = velocityMap[index + 1];
          }
        }

        // 获取物体速度
        const velocity = physicsBody.getVelocity();

        // 计算相对速度
        const relativeVelocity = new THREE.Vector3().copy(flowVelocity).sub(velocity);

        // 计算冲击力
        const impactForce = new THREE.Vector3()
          .copy(relativeVelocity)
          .normalize()
          .multiplyScalar(density * relativeVelocity.lengthSq() * bodySize.x * bodySize.z * 0.5 * submergedRatio);

        // 应用冲击力
        physicsBody.applyForce(impactForce);

        // 如果冲击力足够大，触发水流冲击事件
        if (impactForce.length() > 10) {
          this.eventEmitter.emit(WaterPhysicsSystemEventType.WATER_FLOW_IMPACT, {
            entityId: entityId,
            position: new THREE.Vector3().copy(bodyPosition),
            force: new THREE.Vector3().copy(impactForce),
            waterBody: waterBody
          });
        }
      }
    }
  }

  /**
   * 更新水体碰撞
   * @param waterBody 水体组件
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateWaterCollision(waterBody: WaterBodyComponent, deltaTime: number): void {
    // 获取物理系统
    const physicsSystem = this.world.getSystem('PhysicsSystem');
    if (!physicsSystem) return;

    // 获取水体属性
    const position = waterBody.getPosition();
    const size = waterBody.getSize();
    const heightMap = waterBody.getHeightMap();
    const resolution = waterBody.getResolution();

    // 获取所有物理体
    const bodies = physicsSystem.getBodies();
    if (!bodies || bodies.size === 0) return;

    // 遍历所有物理体
    for (const [entityId, physicsBody] of bodies) {
      // 获取物理体位置、速度和尺寸
      const bodyPosition = physicsBody.getPosition();
      const bodyVelocity = physicsBody.getVelocity();
      const bodySize = physicsBody.getSize();
      const bodyMass = physicsBody.getMass();

      // 如果物理体质量为0（静态物体），则跳过
      if (bodyMass === 0) continue;

      // 检查物体是否在水体范围内或接近水面
      const isNearWaterX = bodyPosition.x + bodySize.x / 2 >= position.x - size.width / 2 &&
                           bodyPosition.x - bodySize.x / 2 <= position.x + size.width / 2;
      const isNearWaterZ = bodyPosition.z + bodySize.z / 2 >= position.z - size.depth / 2 &&
                           bodyPosition.z - bodySize.z / 2 <= position.z + size.depth / 2;

      if (!isNearWaterX || !isNearWaterZ) continue;

      // 计算物体在水体中的位置
      const relativeX = (bodyPosition.x - (position.x - size.width / 2)) / size.width;
      const relativeZ = (bodyPosition.z - (position.z - size.depth / 2)) / size.depth;

      // 计算水面高度
      let waterHeight = position.y;

      // 如果有高度图，使用高度图计算水面高度
      if (heightMap && resolution > 0) {
        const gridX = Math.floor(relativeX * resolution);
        const gridZ = Math.floor(relativeZ * resolution);

        if (gridX >= 0 && gridX < resolution && gridZ >= 0 && gridZ < resolution) {
          const heightIndex = gridZ * resolution + gridX;
          waterHeight += heightMap[heightIndex];
        }
      }

      // 检测物体是否刚刚进入水中
      const prevPosition = new THREE.Vector3().copy(bodyPosition).sub(new THREE.Vector3().copy(bodyVelocity).multiplyScalar(deltaTime));
      const prevY = prevPosition.y - bodySize.y / 2;
      const currY = bodyPosition.y - bodySize.y / 2;

      const wasAboveWater = prevY > waterHeight;
      const isInWater = currY <= waterHeight;

      // 如果物体从水面上方进入水中，且速度足够大，则产生水花
      if (wasAboveWater && isInWater && bodyVelocity.y < -1.0) {
        // 计算碰撞点
        const collisionPoint = new THREE.Vector3(
          bodyPosition.x,
          waterHeight,
          bodyPosition.z
        );

        // 计算碰撞速度
        const collisionSpeed = Math.abs(bodyVelocity.y);

        // 创建水花
        this.createWaterSplash(waterBody, collisionPoint, collisionSpeed, bodySize);
      }
    }
  }

  /**
   * 创建水花
   * @param waterBody 水体组件
   * @param position 位置
   * @param speed 速度
   * @param objectSize 物体尺寸
   */
  private createWaterSplash(waterBody: WaterBodyComponent, position: THREE.Vector3, speed: number, objectSize: THREE.Vector3): void {
    // 如果未启用粒子效果，则跳过
    if (!this.config.enableParticles) return;

    // 获取粒子系统
    const particleSystem = waterBody.getParticleSystem();
    if (!particleSystem) return;

    // 计算粒子数量（基于速度和物体尺寸）
    const particleCount = Math.min(50, Math.floor(speed * 5 * (objectSize.x + objectSize.z) / 2));

    // 计算粒子速度（基于碰撞速度）
    const particleSpeed = Math.min(10, speed * 2);

    // 计算粒子生命周期（基于速度）
    const particleLifetime = Math.min(2.0, 0.5 + speed * 0.2);

    // 计算粒子大小（基于速度和物体尺寸）
    const particleSize = Math.min(0.5, 0.1 + speed * 0.05 * (objectSize.x + objectSize.z) / 4);

    // 创建水花粒子
    for (let i = 0; i < particleCount; i++) {
      // 随机方向
      const angle = Math.random() * Math.PI * 2;
      const radius = Math.random() * objectSize.x * 0.5;

      // 计算粒子位置（在碰撞点附近）
      const particlePosition = new THREE.Vector3(
        position.x + Math.cos(angle) * radius,
        position.y + Math.random() * 0.1,
        position.z + Math.sin(angle) * radius
      );

      // 计算粒子速度（向上和向外）
      const particleVelocity = new THREE.Vector3(
        Math.cos(angle) * particleSpeed * (0.3 + Math.random() * 0.7),
        particleSpeed * (0.5 + Math.random() * 0.5),
        Math.sin(angle) * particleSpeed * (0.3 + Math.random() * 0.7)
      );

      // 创建粒子
      particleSystem.createWaterParticle(
        particlePosition,
        particleVelocity,
        particleSize * (0.5 + Math.random() * 0.5),
        particleLifetime * (0.8 + Math.random() * 0.4)
      );
    }

    // 创建水波纹
    this.createWaterRipple(waterBody, position, speed, objectSize);
  }

  /**
   * 创建水波纹
   * @param waterBody 水体组件
   * @param position 位置
   * @param speed 速度
   * @param objectSize 物体尺寸
   */
  private createWaterRipple(waterBody: WaterBodyComponent, position: THREE.Vector3, speed: number, objectSize: THREE.Vector3): void {
    // 计算波纹强度（基于速度和物体尺寸）
    const rippleStrength = Math.min(1.0, speed * 0.1 * (objectSize.x + objectSize.z) / 4);

    // 计算波纹半径（基于物体尺寸）
    const rippleRadius = Math.max(0.5, (objectSize.x + objectSize.z) / 4);

    // 添加波纹到水体
    waterBody.addRipple(position, rippleRadius, rippleStrength);

    // 如果启用了水体分裂且速度足够大，创建水体分裂效果
    if (this.config.enableWaterSplitting && speed > 5.0) {
      this.createWaterSplitting(waterBody, position, speed, objectSize);
    }
  }

  /**
   * 创建水体分裂效果
   * @param waterBody 水体组件
   * @param position 位置
   * @param speed 速度
   * @param objectSize 物体尺寸
   */
  private createWaterSplitting(waterBody: WaterBodyComponent, position: THREE.Vector3, speed: number, objectSize: THREE.Vector3): void {
    // 计算分裂强度（基于速度和物体尺寸）
    const splittingStrength = Math.min(1.0, speed * 0.1 * (objectSize.x + objectSize.z) / 4);

    // 计算分裂半径（基于物体尺寸和速度）
    const splittingRadius = Math.max(1.0, (objectSize.x + objectSize.z) / 2 * (speed / 10));

    // 计算分裂持续时间（基于速度）
    const splittingDuration = Math.min(2.0, 0.5 + speed * 0.1);

    // 创建分裂效果
    const splittingEffect = {
      position: position.clone(),
      radius: splittingRadius,
      strength: splittingStrength,
      duration: splittingDuration,
      time: 0,
      update: (deltaTime: number) => {
        // 更新时间
        splittingEffect.time += deltaTime;

        // 计算当前强度（随时间衰减）
        const currentStrength = splittingEffect.strength * (1 - splittingEffect.time / splittingEffect.duration);

        // 计算当前半径（随时间增加）
        const currentRadius = splittingEffect.radius * (1 + splittingEffect.time / splittingEffect.duration);

        // 更新水面高度图
        const heightMap = waterBody.getHeightMap();
        const resolution = waterBody.getResolution();
        const waterPosition = waterBody.getPosition();
        const waterSize = waterBody.getSize();

        if (heightMap && resolution > 0) {
          // 计算分裂效果在水体中的相对位置
          const relativeX = (splittingEffect.position.x - (waterPosition.x - waterSize.width / 2)) / waterSize.width;
          const relativeZ = (splittingEffect.position.z - (waterPosition.z - waterSize.depth / 2)) / waterSize.depth;

          // 计算分裂效果在网格中的位置
          const centerX = Math.floor(relativeX * resolution);
          const centerZ = Math.floor(relativeZ * resolution);

          // 计算分裂效果在网格中的半径
          const gridRadius = Math.ceil(currentRadius / Math.max(waterSize.width, waterSize.depth) * resolution);

          // 更新分裂效果范围内的高度图
          for (let z = Math.max(0, centerZ - gridRadius); z <= Math.min(resolution - 1, centerZ + gridRadius); z++) {
            for (let x = Math.max(0, centerX - gridRadius); x <= Math.min(resolution - 1, centerX + gridRadius); x++) {
              // 计算到分裂中心的距离
              const dx = (x - centerX) / resolution * waterSize.width;
              const dz = (z - centerZ) / resolution * waterSize.depth;
              const distance = Math.sqrt(dx * dx + dz * dz);

              // 如果在分裂半径内，更新高度
              if (distance <= currentRadius) {
                // 计算高度偏移（基于距离和强度）
                const heightOffset = currentStrength * (1 - distance / currentRadius) * Math.sin(distance / currentRadius * Math.PI);

                // 更新高度图
                const index = z * resolution + x;
                heightMap[index] += heightOffset;
              }
            }
          }

          // 更新水体的高度图
          waterBody.setHeightMap(heightMap);

          // 更新法线图
          this.updateNormalMap(waterBody);

          // 更新水体网格
          waterBody.updateWaterMesh();
        }

        // 返回是否完成
        return splittingEffect.time >= splittingEffect.duration;
      }
    };

    // 添加分裂效果到水体
    waterBody.addSplittingEffect(splittingEffect);

    // 触发水体分裂事件
    this.eventEmitter.emit(WaterPhysicsSystemEventType.WATER_SPLITTING, {
      position: position.clone(),
      radius: splittingRadius,
      strength: splittingStrength,
      waterBody: waterBody
    });
  }

  /**
   * 更新水体粒子效果
   * @param waterBody 水体组件
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateWaterParticles(waterBody: WaterBodyComponent, deltaTime: number): void {
    // 获取粒子系统
    const particleSystem = waterBody.getParticleSystem();
    if (!particleSystem) return;

    // 更新粒子系统
    particleSystem.update(deltaTime);
  }
}