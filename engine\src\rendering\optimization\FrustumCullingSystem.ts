/**
 * 视锥体剔除系统
 * 用于剔除视锥体外的物体，提高渲染性能
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { Camera   } from '../Camera';
import { Scene } from '../../scene/Scene';
import type { Transform } from '../../scene/Transform';
import { EventEmitter } from '../../utils/EventEmitter';
import { Octree } from './spatial/Octree';
import { CullableComponent } from './CullableComponent';

/**
 * 视锥体剔除系统配置接口
 */
export interface FrustumCullingSystemOptions {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率（帧） */
  updateFrequency?: number;
  /** 是否使用八叉树 */
  useOctree?: boolean;
  /** 是否使用遮挡剔除 */
  useOcclusionCulling?: boolean;
  /** 是否使用包围盒剔除 */
  useBoundingBoxCulling?: boolean;
  /** 是否使用包围球剔除 */
  useBoundingSphereCulling?: boolean;
  /** 是否使用距离剔除 */
  useDistanceCulling?: boolean;
  /** 最大剔除距离 */
  maxCullingDistance?: number;
}

/**
 * 视锥体剔除系统事件类型
 */
export enum FrustumCullingSystemEventType {
  /** 实体被剔除 */
  ENTITY_CULLED = 'entity_culled',
  /** 实体被恢复 */
  ENTITY_RESTORED = 'entity_restored',
  /** 组件添加 */
  COMPONENT_ADDED = 'component_added',
  /** 组件移除 */
  COMPONENT_REMOVED = 'component_removed'
}

/**
 * 视锥体剔除系统类
 */
export class FrustumCullingSystem extends System {
  /** 系统类型 */
  private static readonly TYPE: string = 'FrustumCullingSystem';

  /** 是否自动更新 */
  private autoUpdate: boolean;

  /** 更新频率（帧） */
  private updateFrequency: number;

  /** 当前帧计数 */
  private frameCount: number = 0;

  /** 是否使用八叉树 */
  private useOctree: boolean;

  /** 是否使用遮挡剔除 */
  private useOcclusionCulling: boolean;

  /** 是否使用包围盒剔除 */
  private useBoundingBoxCulling: boolean;

  /** 是否使用包围球剔除 */
  private useBoundingSphereCulling: boolean;

  /** 是否使用距离剔除 */
  private useDistanceCulling: boolean;

  /** 最大剔除距离 */
  private maxCullingDistance: number;

  /** 活跃相机 */
  private activeCamera: Camera | null = null;

  /** 活跃场景 */
  private activeScene: Scene | null = null;

  /** 可剔除组件列表 */
  private cullableComponents: Map<Entity, CullableComponent> = new Map();

  /** 已剔除实体列表 */
  private culledEntities: Set<Entity> = new Set();

  /** 视锥体 */
  private frustum: THREE.Frustum = new THREE.Frustum();

  /** 八叉树 */
  private octree: Octree | null = null;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 临时向量 */
  private tempVector: THREE.Vector3 = new THREE.Vector3();

  /** 临时矩阵 */
  private tempMatrix: THREE.Matrix4 = new THREE.Matrix4();

  /** 临时包围球 */
  private tempSphere: THREE.Sphere = new THREE.Sphere();

  /** 临时包围盒 */
  private tempBox: THREE.Box3 = new THREE.Box3();

  /**
   * 创建视锥体剔除系统
   * @param options 视锥体剔除系统配置
   */
  constructor(options: FrustumCullingSystemOptions = {}) {
    super();

    this.setEnabled(options.enabled !== undefined ? options.enabled : true);
    this.autoUpdate = options.autoUpdate !== undefined ? options.autoUpdate : true;
    this.updateFrequency = options.updateFrequency !== undefined ? options.updateFrequency : 1;
    this.useOctree = options.useOctree !== undefined ? options.useOctree : true;
    this.useOcclusionCulling = options.useOcclusionCulling !== undefined ? options.useOcclusionCulling : false;
    this.useBoundingBoxCulling = options.useBoundingBoxCulling !== undefined ? options.useBoundingBoxCulling : true;
    this.useBoundingSphereCulling = options.useBoundingSphereCulling !== undefined ? options.useBoundingSphereCulling : true;
    this.useDistanceCulling = options.useDistanceCulling !== undefined ? options.useDistanceCulling : true;
    this.maxCullingDistance = options.maxCullingDistance !== undefined ? options.maxCullingDistance : 1000;

    // 设置优先级
    this.setPriority(20); // 视锥体剔除系统优先级较高，在LOD系统之后执行
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return FrustumCullingSystem.TYPE;
  }

  /**
   * 设置活跃相机
   * @param camera 相机
   */
  public setActiveCamera(camera: Camera): void {
    this.activeCamera = camera;
  }

  /**
   * 获取活跃相机
   * @returns 活跃相机
   */
  public getActiveCamera(): Camera | null {
    return this.activeCamera;
  }

  /**
   * 设置活跃场景
   * @param scene 场景
   */
  public setActiveScene(scene: Scene): void {
    this.activeScene = scene;

    // 如果使用八叉树，则重建八叉树
    if (this.useOctree) {
      this.rebuildOctree();
    }
  }

  /**
   * 获取活跃场景
   * @returns 活跃场景
   */
  public getActiveScene(): Scene | null {
    return this.activeScene;
  }

  /**
   * 注册可剔除组件
   * @param entity 实体
   * @param component 可剔除组件
   */
  public registerCullableComponent(entity: Entity, component: CullableComponent): void {
    this.cullableComponents.set(entity, component);
    this.eventEmitter.emit(FrustumCullingSystemEventType.COMPONENT_ADDED, entity, component);

    // 如果使用八叉树，则将实体添加到八叉树
    if (this.useOctree && this.octree) {
      const transform = entity.getComponent('Transform') as any as Transform;
      if (transform) {
        const position = this.tempVector.copy(transform.getWorldPosition());
        const boundingRadius = component.getBoundingRadius();
        this.octree.insert(entity, position, boundingRadius);
      }
    }
  }

  /**
   * 注销可剔除组件
   * @param entity 实体
   */
  public unregisterCullableComponent(entity: Entity): void {
    const component = this.cullableComponents.get(entity);
    if (component) {
      this.cullableComponents.delete(entity);
      this.eventEmitter.emit(FrustumCullingSystemEventType.COMPONENT_REMOVED, entity, component);

      // 如果实体已被剔除，则恢复实体
      if (this.culledEntities.has(entity)) {
        this.restoreEntity(entity);
      }

      // 如果使用八叉树，则从八叉树中移除实体
      if (this.useOctree && this.octree) {
        this.octree.remove(entity);
      }
    }
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.isEnabled() || !this.autoUpdate) {
      return;
    }

    // 如果没有活跃相机或场景，则不更新
    if (!this.activeCamera || !this.activeScene) {
      return;
    }

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.updateFrequency !== 0) {
      return;
    }

    // 更新视锥体
    this.updateFrustum();

    // 更新剔除
    this.updateCulling();
  }

  /**
   * 更新视锥体
   */
  private updateFrustum(): void {
    if (!this.activeCamera) {
      return;
    }

    const camera = this.activeCamera.getThreeCamera();
    this.tempMatrix.multiplyMatrices(camera.projectionMatrix, camera.matrixWorldInverse);
    this.frustum.setFromProjectionMatrix(this.tempMatrix);
  }

  /**
   * 更新剔除
   */
  private updateCulling(): void {
    if (!this.activeCamera) {
      return;
    }

    const cameraPosition = this.activeCamera.getThreeCamera().position;

    // 如果使用八叉树，则使用八叉树进行剔除
    if (this.useOctree && this.octree) {
      this.updateOctreeCulling(cameraPosition);
    } else {
      // 否则，遍历所有可剔除组件
      this.updateBruteForceCulling(cameraPosition);
    }
  }

  /**
   * 使用八叉树进行剔除
   * @param cameraPosition 相机位置
   */
  private updateOctreeCulling(cameraPosition: THREE.Vector3): void {
    if (!this.octree) {
      return;
    }

    // 获取视锥体内的实体
    const visibleEntities = this.octree.queryFrustum(this.frustum);
    const visibleEntitySet = new Set(visibleEntities);

    // 恢复所有应该可见的实体
    for (const entity of visibleEntities) {
      if (this.culledEntities.has(entity)) {
        this.restoreEntity(entity);
      }
    }

    // 剔除所有应该不可见的实体
    for (const [entity, component] of Array.from(this.cullableComponents.entries())) {
      if (!visibleEntitySet.has(entity) && !this.culledEntities.has(entity)) {
        this.cullEntity(entity);
      }
    }
  }

  /**
   * 使用暴力方法进行剔除
   * @param cameraPosition 相机位置
   */
  private updateBruteForceCulling(cameraPosition: THREE.Vector3): void {
    // 遍历所有可剔除组件
    for (const [entity, component] of Array.from(this.cullableComponents.entries())) {
      // 如果实体没有变换组件，则跳过
      const transform = entity.getComponent('Transform') as any as Transform;
      if (!transform) {
        continue;
      }

      // 获取实体的世界位置
      const position = this.tempVector.copy(transform.getWorldPosition());

      // 如果使用距离剔除，检查实体是否超出最大剔除距离
      if (this.useDistanceCulling) {
        const distance = position.distanceTo(cameraPosition);
        if (distance > this.maxCullingDistance) {
          if (!this.culledEntities.has(entity)) {
            this.cullEntity(entity);
          }
          continue;
        }
      }

      // 如果使用包围球剔除，检查实体的包围球是否与视锥体相交
      if (this.useBoundingSphereCulling) {
        this.tempSphere.center.copy(position);
        this.tempSphere.radius = component.getBoundingRadius();

        if (!this.frustum.intersectsSphere(this.tempSphere)) {
          if (!this.culledEntities.has(entity)) {
            this.cullEntity(entity);
          }
          continue;
        }
      }

      // 如果使用包围盒剔除，检查实体的包围盒是否与视锥体相交
      if (this.useBoundingBoxCulling && component.getBoundingBox()) {
        this.tempBox.copy(component.getBoundingBox()!);
        this.tempBox.applyMatrix4(transform.getWorldMatrix());

        if (!this.frustum.intersectsBox(this.tempBox)) {
          if (!this.culledEntities.has(entity)) {
            this.cullEntity(entity);
          }
          continue;
        }
      }

      // 如果实体应该可见，但当前被剔除，则恢复实体
      if (this.culledEntities.has(entity)) {
        this.restoreEntity(entity);
      }
    }
  }

  /**
   * 剔除实体
   * @param entity 实体
   */
  private cullEntity(entity: Entity): void {
    // 获取可剔除组件
    const component = this.cullableComponents.get(entity);
    if (!component) {
      return;
    }

    // 设置实体不可见
    component.setVisible(false);

    // 添加到已剔除实体列表
    this.culledEntities.add(entity);

    // 发出实体被剔除事件
    this.eventEmitter.emit(FrustumCullingSystemEventType.ENTITY_CULLED, entity, component);
  }

  /**
   * 恢复实体
   * @param entity 实体
   */
  private restoreEntity(entity: Entity): void {
    // 获取可剔除组件
    const component = this.cullableComponents.get(entity);
    if (!component) {
      return;
    }

    // 设置实体可见
    component.setVisible(true);

    // 从已剔除实体列表中移除
    this.culledEntities.delete(entity);

    // 发出实体被恢复事件
    this.eventEmitter.emit(FrustumCullingSystemEventType.ENTITY_RESTORED, entity, component);
  }

  /**
   * 重建八叉树
   */
  private rebuildOctree(): void {
    if (!this.useOctree || !this.activeScene) {
      return;
    }

    // 创建八叉树
    this.octree = new Octree({
      maxDepth: 8,
      maxObjectsPerNode: 16,
      minNodeSize: 1
    });

    // 将所有可剔除组件添加到八叉树
    for (const [entity, component] of Array.from(this.cullableComponents.entries())) {
      const transform = entity.getComponent('Transform') as any as Transform;
      if (transform) {
        const position = this.tempVector.copy(transform.getWorldPosition());
        const boundingRadius = component.getBoundingRadius();
        this.octree.insert(entity, position, boundingRadius);
      }
    }
  }

  /**
   * 手动更新剔除
   */
  public manualUpdate(): void {
    if (!this.isEnabled()) {
      return;
    }

    this.updateFrustum();
    this.updateCulling();
  }

  /**
   * 设置是否启用
   * @param enabled 是否启用
   */
  public setEnabled(enabled: boolean): void {
    super.setEnabled(enabled);

    // 如果禁用，则恢复所有已剔除的实体
    if (!enabled) {
      for (const entity of Array.from(this.culledEntities)) {
        this.restoreEntity(entity);
      }
    }
  }

  /**
   * 设置是否自动更新
   * @param autoUpdate 是否自动更新
   */
  public setAutoUpdate(autoUpdate: boolean): void {
    this.autoUpdate = autoUpdate;
  }

  /**
   * 获取是否自动更新
   * @returns 是否自动更新
   */
  public isAutoUpdate(): boolean {
    return this.autoUpdate;
  }

  /**
   * 设置更新频率
   * @param frequency 更新频率
   */
  public setUpdateFrequency(frequency: number): void {
    this.updateFrequency = frequency;
  }

  /**
   * 获取更新频率
   * @returns 更新频率
   */
  public getUpdateFrequency(): number {
    return this.updateFrequency;
  }

  /**
   * 设置是否使用八叉树
   * @param useOctree 是否使用八叉树
   */
  public setUseOctree(useOctree: boolean): void {
    this.useOctree = useOctree;

    // 如果启用八叉树，则重建八叉树
    if (useOctree) {
      this.rebuildOctree();
    } else {
      this.octree = null;
    }
  }

  /**
   * 获取是否使用八叉树
   * @returns 是否使用八叉树
   */
  public isUseOctree(): boolean {
    return this.useOctree;
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public addEventListener(type: FrustumCullingSystemEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(type, listener);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public removeEventListener(type: FrustumCullingSystemEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(type, listener);
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 恢复所有已剔除的实体
    for (const entity of this.culledEntities) {
      this.restoreEntity(entity);
    }

    this.cullableComponents.clear();
    this.culledEntities.clear();
    this.octree = null;
    this.eventEmitter.removeAllListeners();
  }
}
