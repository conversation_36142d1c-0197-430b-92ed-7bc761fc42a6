/**
 * 高级实例化渲染系统
 * 支持更多类型的实例化对象和高级渲染功能
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { Camera   } from '../Camera';
import { Scene } from '../../scene/Scene';
import type { Transform } from '../../scene/Transform';
import { InstancedComponent, InstanceData } from './InstancedComponent';
import { EnhancedInstancedRenderingSystem, EnhancedInstancedRenderingSystemOptions } from './EnhancedInstancedRenderingSystem';
import { Debug } from '../../utils/Debug';
import { EventEmitter } from '../../utils/EventEmitter';
import { GPUInstanceUpdater } from './GPUInstanceUpdater';
import { LODComponent, LODLevel } from './LODComponent';

/**
 * 高级实例数据接口
 */
export interface AdvancedInstanceData {
  /** 位置 */
  position: THREE.Vector3;
  /** 旋转（四元数） */
  quaternion: THREE.Quaternion;
  /** 缩放 */
  scale: THREE.Vector3;
  /** 颜色 */
  color?: THREE.Color;
  /** 自定义属性 */
  customAttributes?: Record<string, any>;
  /** 材质覆盖 */
  materialOverrides?: Record<string, any>;
  /** LOD配置 */
  lodConfig?: {
    /** 距离系数 */
    distanceFactor?: number;
    /** 是否启用 */
    enabled?: boolean;
  };
  /** 是否可见 */
  visible?: boolean;
  /** 是否投射阴影 */
  castShadow?: boolean;
  /** 是否接收阴影 */
  receiveShadow?: boolean;
  /** 是否使用物理 */
  usePhysics?: boolean;
  /** 物理属性 */
  physicsProperties?: {
    /** 质量 */
    mass?: number;
    /** 是否是静态的 */
    isStatic?: boolean;
    /** 碰撞形状 */
    collisionShape?: 'box' | 'sphere' | 'capsule' | 'cylinder' | 'convexHull' | 'mesh';
    /** 摩擦系数 */
    friction?: number;
    /** 恢复系数 */
    restitution?: number;
  };
  /** 用户数据 */
  userData?: any;
}

/**
 * 高级实例化渲染系统配置接口
 */
export interface AdvancedInstancedRenderingSystemOptions extends EnhancedInstancedRenderingSystemOptions {
  /** 是否支持自定义着色器 */
  supportCustomShaders?: boolean;
  /** 是否支持材质变体 */
  supportMaterialVariants?: boolean;
  /** 是否支持实例LOD */
  supportInstanceLOD?: boolean;
  /** 是否支持实例动画 */
  supportInstanceAnimation?: boolean;
  /** 是否支持实例物理 */
  supportInstancePhysics?: boolean;
  /** 是否使用高级剔除 */
  useAdvancedCulling?: boolean;
  /** 是否使用实例缓存 */
  useInstanceCache?: boolean;
  /** 是否使用实例合并 */
  useInstanceMerging?: boolean;
  /** 是否使用自动实例化 */
  useAutoInstancing?: boolean;
}

/**
 * 材质变体接口
 */
export interface MaterialVariant {
  /** 变体ID */
  id: string;
  /** 原始材质 */
  originalMaterial: THREE.Material;
  /** 变体材质 */
  variantMaterial: THREE.Material;
  /** 属性映射 */
  propertyMap: Record<string, string>;
}

/**
 * 高级实例化渲染系统
 * 支持更多类型的实例化对象和高级渲染功能
 */
export class AdvancedInstancedRenderingSystem extends EnhancedInstancedRenderingSystem {
  /** 系统类型 */
  public static readonly TYPE: string = 'AdvancedInstancedRenderingSystem';

  /** 是否支持自定义着色器 */
  private supportCustomShaders: boolean;
  /** 是否支持材质变体 */
  private supportMaterialVariants: boolean;
  /** 是否支持实例LOD */
  private supportInstanceLOD: boolean;
  /** 是否支持实例动画 */
  private supportInstanceAnimation: boolean;
  /** 是否支持实例物理 */
  private supportInstancePhysics: boolean;
  /** 是否使用高级剔除 */
  private useAdvancedCulling: boolean;
  /** 是否使用实例缓存 */
  private useInstanceCache: boolean;
  /** 是否使用实例合并 */
  private useInstanceMerging: boolean;
  /** 是否使用自动实例化 */
  private useAutoInstancing: boolean;
  /** 材质变体映射 */
  private materialVariants: Map<string, MaterialVariant>;
  /** 自定义着色器映射 */
  private customShaders: Map<string, THREE.ShaderMaterial>;
  /** 实例LOD映射 */
  private instanceLODs: Map<string, THREE.InstancedMesh[]>;
  /** 实例缓存 */
  private instanceCache: Map<string, THREE.InstancedMesh>;
  /** 事件发射器 */
  private eventEmitter: EventEmitter;

  /**
   * 创建高级实例化渲染系统
   * @param options 系统选项
   */
  constructor(options: AdvancedInstancedRenderingSystemOptions = {}) {
    super(options);

    // 设置高级选项
    this.supportCustomShaders = options.supportCustomShaders !== undefined ? options.supportCustomShaders : true;
    this.supportMaterialVariants = options.supportMaterialVariants !== undefined ? options.supportMaterialVariants : true;
    this.supportInstanceLOD = options.supportInstanceLOD !== undefined ? options.supportInstanceLOD : true;
    this.supportInstanceAnimation = options.supportInstanceAnimation !== undefined ? options.supportInstanceAnimation : true;
    this.supportInstancePhysics = options.supportInstancePhysics !== undefined ? options.supportInstancePhysics : false;
    this.useAdvancedCulling = options.useAdvancedCulling !== undefined ? options.useAdvancedCulling : true;
    this.useInstanceCache = options.useInstanceCache !== undefined ? options.useInstanceCache : true;
    this.useInstanceMerging = options.useInstanceMerging !== undefined ? options.useInstanceMerging : true;
    this.useAutoInstancing = options.useAutoInstancing !== undefined ? options.useAutoInstancing : false;

    // 初始化映射
    this.materialVariants = new Map<string, MaterialVariant>();
    this.customShaders = new Map<string, THREE.ShaderMaterial>();
    this.instanceLODs = new Map<string, THREE.InstancedMesh[]>();
    this.instanceCache = new Map<string, THREE.InstancedMesh>();
    this.eventEmitter = new EventEmitter();

    // 检查功能支持
    this.checkFeatureSupport();
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return AdvancedInstancedRenderingSystem.TYPE;
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    super.initialize();

    // 如果使用自动实例化，扫描场景查找可实例化对象
    if (this.useAutoInstancing) {
      this.scanSceneForInstancing();
    }
  }

  /**
   * 检查功能支持
   */
  private checkFeatureSupport(): void {
    // 检查是否支持实例化渲染
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl2');

    if (!gl) {
      Debug.warn('AdvancedInstancedRenderingSystem', '不支持WebGL2，某些功能可能不可用');
      this.supportCustomShaders = false;
      this.supportMaterialVariants = false;
      return;
    }

    // 检查是否支持实例化数组
    const instancedArrays = gl.getExtension('ANGLE_instanced_arrays');
    if (!instancedArrays) {
      Debug.warn('AdvancedInstancedRenderingSystem', '不支持实例化数组，已禁用实例化渲染');
      this.enabled = false;
      return;
    }

    // 检查是否支持顶点数组对象
    const vao = gl.getExtension('OES_vertex_array_object');
    if (!vao) {
      Debug.warn('AdvancedInstancedRenderingSystem', '不支持顶点数组对象，某些功能可能不可用');
    }

    // 检查是否支持浮点纹理
    const floatTextures = gl.getExtension('OES_texture_float');
    if (!floatTextures) {
      Debug.warn('AdvancedInstancedRenderingSystem', '不支持浮点纹理，已禁用GPU实例更新');
      this.useGPUInstanceUpdate = false;
    }
  }

  /**
   * 扫描场景查找可实例化对象
   */
  private scanSceneForInstancing(): void {
    // 获取场景
    const scene = this.getScene();
    if (!scene) {
      return;
    }

    // 获取场景中的所有实体
    const entities = this.entityManager.getEntities();

    // 创建几何体映射，用于查找相同几何体的网格
    const geometryMap = new Map<string, { mesh: THREE.Mesh; entities: Entity[] }>();

    // 遍历所有实体
    for (const entity of entities) {
      // 获取变换组件
      const transform = entity.getComponent('Transform') as any as any as any as Transform;
      if (!transform) {
        continue;
      }

      // 获取网格
      const mesh = this.getMeshFromEntity(entity);
      if (!mesh || !(mesh instanceof THREE.Mesh)) {
        continue;
      }

      // 如果已经是实例化网格，则跳过
      if (mesh instanceof THREE.InstancedMesh) {
        continue;
      }

      // 如果实体已经有实例化组件，则跳过
      if (entity.hasComponent('InstancedComponent')) {
        continue;
      }

      // 获取几何体和材质的唯一标识
      const geometryId = mesh.geometry.uuid;
      const materialId = Array.isArray(mesh.material)
        ? mesh.material.map(m => m.uuid).join('_')
        : mesh.material.uuid;
      const key = `${geometryId}_${materialId}`;

      // 添加到几何体映射
      if (!geometryMap.has(key)) {
        geometryMap.set(key, { mesh, entities: [] });
      }
      geometryMap.get(key)!.entities.push(entity);
    }

    // 查找可实例化的对象组
    for (const [key, group] of geometryMap.entries()) {
      // 如果实体数量大于阈值，则创建实例化网格
      if (group.entities.length >= this.instanceThreshold) {
        this.createInstancedMeshFromGroup(key, group.mesh, group.entities);
      }
    }
  }

  /**
   * 从实体获取网格
   * @param entity 实体
   * @returns 网格
   */
  private getMeshFromEntity(entity: Entity): THREE.Object3D | null {
    // 这里需要根据具体的场景图实现来获取网格
    // 这只是一个示例实现
    const transform = entity.getComponent('Transform') as any as any as any as Transform;
    if (!transform) {
      return null;
    }

    return transform.getObject3D();
  }

  /**
   * 从组创建实例化网格
   * @param key 组键
   * @param mesh 原始网格
   * @param entities 实体列表
   */
  private createInstancedMeshFromGroup(key: string, mesh: THREE.Mesh, entities: Entity[]): void {
    // 创建实例化网格
    const instancedMesh = new THREE.InstancedMesh(
      mesh.geometry,
      mesh.material,
      entities.length
    );
    instancedMesh.name = `${mesh.name || 'Mesh'}_Instanced`;
    instancedMesh.castShadow = mesh.castShadow;
    instancedMesh.receiveShadow = mesh.receiveShadow;
    instancedMesh.frustumCulled = this.useFrustumCulling;

    // 设置实例矩阵
    const matrix = new THREE.Matrix4();
    for (let i = 0; i < entities.length; i++) {
      const entity = entities[i];
      const transform = entity.getComponent('Transform') as any as any as any as Transform;
      if (transform) {
        const position = transform.getPosition();
        const rotation = transform.getRotation();
        const scale = transform.getScale();

        matrix.compose(
          new THREE.Vector3(position.x, position.y, position.z),
          new THREE.Quaternion(rotation.x, rotation.y, rotation.z, rotation.w),
          new THREE.Vector3(scale.x, scale.y, scale.z)
        );

        instancedMesh.setMatrixAt(i, matrix);
      }
    }

    // 更新实例矩阵
    instancedMesh.instanceMatrix.needsUpdate = true;

    // 添加到场景
    const scene = this.getScene();
    if (scene) {
      scene.getThreeScene().add(instancedMesh);
    }

    // 添加到缓存
    if (this.useInstanceCache) {
      this.instanceCache.set(key, instancedMesh);
    }

    Debug.log('AdvancedInstancedRenderingSystem', `自动创建实例化网格: ${key}, 实例数: ${entities.length}`);
  }

  /**
   * 创建材质变体
   * @param originalMaterial 原始材质
   * @param variantProperties 变体属性
   * @returns 变体ID
   */
  public createMaterialVariant(originalMaterial: THREE.Material, variantProperties: Record<string, any>): string {
    if (!this.supportMaterialVariants) {
      Debug.warn('AdvancedInstancedRenderingSystem', '不支持材质变体');
      return '';
    }

    // 克隆原始材质
    const variantMaterial = originalMaterial.clone();

    // 应用变体属性
    const propertyMap: Record<string, string> = {};
    for (const [key, value] of Object.entries(variantProperties)) {
      if (key in variantMaterial) {
        (variantMaterial as any)[key] = value;
        propertyMap[key] = key;
      }
    }

    // 生成变体ID
    const variantId = `variant_${originalMaterial.uuid}_${Date.now()}`;

    // 创建材质变体
    const variant: MaterialVariant = {
      id: variantId,
      originalMaterial,
      variantMaterial,
      propertyMap
    };

    // 添加到映射
    this.materialVariants.set(variantId, variant);

    return variantId;
  }

  /**
   * 创建自定义着色器
   * @param name 着色器名称
   * @param vertexShader 顶点着色器
   * @param fragmentShader 片段着色器
   * @param uniforms 统一变量
   * @returns 着色器ID
   */
  public createCustomShader(name: string, vertexShader: string, fragmentShader: string, uniforms: Record<string, THREE.IUniform>): string {
    if (!this.supportCustomShaders) {
      Debug.warn('AdvancedInstancedRenderingSystem', '不支持自定义着色器');
      return '';
    }

    // 创建着色器材质
    const shaderMaterial = new THREE.ShaderMaterial({
      vertexShader,
      fragmentShader,
      uniforms,
      transparent: true,
      side: THREE.DoubleSide
    });

    // 生成着色器ID
    const shaderId = `shader_${name}_${Date.now()}`;

    // 添加到映射
    this.customShaders.set(shaderId, shaderMaterial);

    return shaderId;
  }

  /**
   * 创建高级实例
   * @param geometry 几何体
   * @param material 材质
   * @param instanceData 实例数据
   * @returns 实例ID
   */
  public createAdvancedInstance(geometry: THREE.BufferGeometry, material: THREE.Material, instanceData: AdvancedInstanceData): string {
    // 生成实例ID
    const instanceId = `instance_${this.instanceCounter++}`;

    // 创建基本实例
    const baseInstanceId = this.addInstance(geometry, material, {
      position: instanceData.position,
      quaternion: instanceData.quaternion,
      scale: instanceData.scale,
      color: instanceData.color
    });

    // 如果支持实例LOD，创建LOD实例
    if (this.supportInstanceLOD && instanceData.lodConfig && instanceData.lodConfig.enabled) {
      this.createInstanceLOD(instanceId, geometry, material, instanceData);
    }

    // 如果支持实例物理，创建物理实例
    if (this.supportInstancePhysics && instanceData.usePhysics) {
      this.createInstancePhysics(instanceId, geometry, instanceData);
    }

    return instanceId;
  }

  /**
   * 创建实例LOD
   * @param instanceId 实例ID
   * @param geometry 几何体
   * @param material 材质
   * @param instanceData 实例数据
   */
  private createInstanceLOD(instanceId: string, geometry: THREE.BufferGeometry, material: THREE.Material, instanceData: AdvancedInstanceData): void {
    // 创建LOD级别
    const lodMeshes: THREE.InstancedMesh[] = [];

    // 创建高细节LOD
    const highLOD = new THREE.InstancedMesh(geometry, material, 1);
    highLOD.name = `${instanceId}_LOD_HIGH`;
    highLOD.setMatrixAt(0, new THREE.Matrix4().compose(
      instanceData.position,
      instanceData.quaternion,
      instanceData.scale
    ));
    highLOD.instanceMatrix.needsUpdate = true;
    lodMeshes.push(highLOD);

    // 创建中等细节LOD
    const mediumGeometry = this.simplifyGeometry(geometry, 0.5);
    const mediumLOD = new THREE.InstancedMesh(mediumGeometry, material, 1);
    mediumLOD.name = `${instanceId}_LOD_MEDIUM`;
    mediumLOD.setMatrixAt(0, new THREE.Matrix4().compose(
      instanceData.position,
      instanceData.quaternion,
      instanceData.scale
    ));
    mediumLOD.instanceMatrix.needsUpdate = true;
    lodMeshes.push(mediumLOD);

    // 创建低细节LOD
    const lowGeometry = this.simplifyGeometry(geometry, 0.2);
    const lowLOD = new THREE.InstancedMesh(lowGeometry, material, 1);
    lowLOD.name = `${instanceId}_LOD_LOW`;
    lowLOD.setMatrixAt(0, new THREE.Matrix4().compose(
      instanceData.position,
      instanceData.quaternion,
      instanceData.scale
    ));
    lowLOD.instanceMatrix.needsUpdate = true;
    lodMeshes.push(lowLOD);

    // 添加到映射
    this.instanceLODs.set(instanceId, lodMeshes);
  }

  /**
   * 简化几何体
   * @param geometry 几何体
   * @param ratio 简化比例
   * @returns 简化后的几何体
   */
  private simplifyGeometry(geometry: THREE.BufferGeometry, ratio: number): THREE.BufferGeometry {
    // 这里应该使用实际的几何体简化算法
    // 这只是一个示例实现
    const simplified = geometry.clone();
    
    // 如果有索引，简化索引
    if (simplified.index) {
      const indices = simplified.index.array;
      const newIndices = new Uint32Array(Math.floor(indices.length * ratio));
      for (let i = 0; i < newIndices.length; i++) {
        newIndices[i] = indices[i];
      }
      simplified.setIndex(new THREE.BufferAttribute(newIndices, 1));
    }
    
    return simplified;
  }

  /**
   * 创建实例物理
   * @param instanceId 实例ID
   * @param geometry 几何体
   * @param instanceData 实例数据
   */
  private createInstancePhysics(instanceId: string, geometry: THREE.BufferGeometry, instanceData: AdvancedInstanceData): void {
    // 这里应该使用实际的物理系统
    // 这只是一个示例实现
    if (!instanceData.physicsProperties) {
      return;
    }

    Debug.log('AdvancedInstancedRenderingSystem', `为实例 ${instanceId} 创建物理属性`);
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    super.update(deltaTime);

    // 如果支持实例LOD，更新LOD
    if (this.supportInstanceLOD) {
      this.updateInstanceLODs();
    }
  }

  /**
   * 更新实例LOD
   */
  private updateInstanceLODs(): void {
    // 获取相机
    const camera = this.getCamera();
    if (!camera) {
      return;
    }

    const cameraPosition = camera.getThreeCamera().position;

    // 遍历所有实例LOD
    for (const [instanceId, lodMeshes] of this.instanceLODs.entries()) {
      if (lodMeshes.length === 0) {
        continue;
      }

      // 获取实例位置
      const matrix = new THREE.Matrix4();
      lodMeshes[0].getMatrixAt(0, matrix);
      const position = new THREE.Vector3();
      const quaternion = new THREE.Quaternion();
      const scale = new THREE.Vector3();
      matrix.decompose(position, quaternion, scale);

      // 计算与相机的距离
      const distance = position.distanceTo(cameraPosition);

      // 根据距离选择LOD级别
      let visibleIndex = 0;
      if (distance > 50) {
        visibleIndex = 2; // 低细节
      } else if (distance > 20) {
        visibleIndex = 1; // 中等细节
      }

      // 更新可见性
      for (let i = 0; i < lodMeshes.length; i++) {
        lodMeshes[i].visible = i === visibleIndex;
      }
    }
  }
}
