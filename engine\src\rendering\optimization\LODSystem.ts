/**
 * LOD（细节层次）系统
 * 根据与相机的距离动态调整模型的细节层次
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { Camera   } from '../Camera';
import { Scene } from '../../scene/Scene';
import type { Transform } from '../../scene/Transform';
import { LODComponent, LODLevel, LODLevelConfig } from './LODComponent';
import { LODGenerator } from './LODGenerator';
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';

/**
 * LOD系统配置接口
 */
export interface LODSystemOptions {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率（帧） */
  updateFrequency?: number;
  /** 是否使用视锥体检查 */
  useFrustumCheck?: boolean;
  /** 是否使用遮挡检查 */
  useOcclusionCheck?: boolean;
  /** 是否使用距离检查 */
  useDistanceCheck?: boolean;
  /** 是否使用屏幕大小检查 */
  useScreenSizeCheck?: boolean;
  /** 是否使用自动LOD生成 */
  useAutoLODGeneration?: boolean;
}

/**
 * LOD系统事件类型
 */
export enum LODSystemEventType {
  /** LOD级别变更 */
  LEVEL_CHANGED = 'level_changed',
  /** LOD组件添加 */
  COMPONENT_ADDED = 'component_added',
  /** LOD组件移除 */
  COMPONENT_REMOVED = 'component_removed'
}

/**
 * LOD系统类
 */
export class LODSystem extends System {
  /** 系统类型 */
  private static readonly TYPE: string = 'LODSystem';

  /** LOD生成器 */
  private lodGenerator: LODGenerator | null = null;

  /** 是否自动更新 */
  private autoUpdate: boolean;

  /** 更新频率（帧） */
  private updateFrequency: number;

  /** 当前帧计数 */
  private frameCount: number = 0;

  /** 是否使用视锥体检查 */
  private useFrustumCheck: boolean;

  /** 是否使用遮挡检查 */
  private useOcclusionCheck: boolean;

  /** 是否使用距离检查 */
  private useDistanceCheck: boolean;

  /** 是否使用屏幕大小检查 */
  private useScreenSizeCheck: boolean;

  /** 是否使用自动LOD生成 */
  private useAutoLODGeneration: boolean;

  /** 活跃相机 */
  private activeCamera: Camera | null = null;

  /** 活跃场景 */
  private activeScene: Scene | null = null;

  /** LOD组件列表 */
  private lodComponents: Map<Entity, LODComponent> = new Map();

  /** 视锥体 */
  private frustum: THREE.Frustum = new THREE.Frustum();

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 临时向量 */
  private tempVector: THREE.Vector3 = new THREE.Vector3();

  /** 临时矩阵 */
  private tempMatrix: THREE.Matrix4 = new THREE.Matrix4();

  /**
   * 创建LOD系统
   * @param options LOD系统配置
   */
  constructor(options: LODSystemOptions = {}) {
    super();

    this.setEnabled(options.enabled !== undefined ? options.enabled : true);
    this.autoUpdate = options.autoUpdate !== undefined ? options.autoUpdate : true;
    this.updateFrequency = options.updateFrequency !== undefined ? options.updateFrequency : 1;
    this.useFrustumCheck = options.useFrustumCheck !== undefined ? options.useFrustumCheck : true;
    this.useOcclusionCheck = options.useOcclusionCheck !== undefined ? options.useOcclusionCheck : false;
    this.useDistanceCheck = options.useDistanceCheck !== undefined ? options.useDistanceCheck : true;
    this.useScreenSizeCheck = options.useScreenSizeCheck !== undefined ? options.useScreenSizeCheck : false;
    this.useAutoLODGeneration = options.useAutoLODGeneration !== undefined ? options.useAutoLODGeneration : false;

    // 创建LOD生成器
    if (this.useAutoLODGeneration) {
      this.lodGenerator = new LODGenerator();
    }

    // 设置优先级
    this.setPriority(10); // LOD系统优先级较高，在渲染系统之前执行
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return LODSystem.TYPE;
  }

  /**
   * 设置活跃相机
   * @param camera 相机
   */
  public setActiveCamera(camera: Camera): void {
    this.activeCamera = camera;
  }

  /**
   * 获取活跃相机
   * @returns 活跃相机
   */
  public getActiveCamera(): Camera | null {
    return this.activeCamera;
  }

  /**
   * 设置活跃场景
   * @param scene 场景
   */
  public setActiveScene(scene: Scene): void {
    this.activeScene = scene;
  }

  /**
   * 获取活跃场景
   * @returns 活跃场景
   */
  public getActiveScene(): Scene | null {
    return this.activeScene;
  }

  /**
   * 注册LOD组件
   * @param entity 实体
   * @param component LOD组件
   */
  public registerLODComponent(entity: Entity, component: LODComponent): void {
    this.lodComponents.set(entity, component);
    this.eventEmitter.emit(LODSystemEventType.COMPONENT_ADDED, entity, component);
  }

  /**
   * 注销LOD组件
   * @param entity 实体
   */
  public unregisterLODComponent(entity: Entity): void {
    const component = this.lodComponents.get(entity);
    if (component) {
      this.lodComponents.delete(entity);
      this.eventEmitter.emit(LODSystemEventType.COMPONENT_REMOVED, entity, component);
    }
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.isEnabled() || !this.autoUpdate) {
      return;
    }

    // 如果没有活跃相机或场景，则不更新
    if (!this.activeCamera || !this.activeScene) {
      return;
    }

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.updateFrequency !== 0) {
      return;
    }

    // 更新视锥体
    if (this.useFrustumCheck) {
      this.updateFrustum();
    }

    // 更新所有LOD组件
    this.updateLODComponents();
  }

  /**
   * 更新视锥体
   */
  private updateFrustum(): void {
    if (!this.activeCamera) {
      return;
    }

    const camera = this.activeCamera.getThreeCamera();
    this.tempMatrix.multiplyMatrices(camera.projectionMatrix, camera.matrixWorldInverse);
    this.frustum.setFromProjectionMatrix(this.tempMatrix);
  }

  /**
   * 更新所有LOD组件
   */
  private updateLODComponents(): void {
    if (!this.activeCamera) {
      return;
    }

    const cameraPosition = this.activeCamera.getThreeCamera().position;

    // 遍历所有LOD组件
    for (const [entity, lodComponent] of Array.from(this.lodComponents.entries())) {
      // 如果实体没有变换组件，则跳过
      const transform = entity.getComponent('Transform') as any as Transform;
      if (!transform) {
        continue;
      }

      // 获取实体的世界位置
      const position = this.tempVector.copy(transform.getWorldPosition());

      // 如果使用视锥体检查，检查实体是否在视锥体内
      if (this.useFrustumCheck) {
        // 创建包围球
        const boundingSphere = new THREE.Sphere(position, lodComponent.getBoundingRadius());

        // 检查包围球是否在视锥体内
        if (!this.frustum.intersectsSphere(boundingSphere)) {
          // 如果不在视锥体内，则隐藏所有级别
          lodComponent.setAllLevelsVisible(false);
          continue;
        }
      }

      // 计算与相机的距离
      const distance = position.distanceTo(cameraPosition);

      // 确定应该显示的LOD级别
      const levelToShow = this.determineLODLevel(lodComponent, distance);

      // 更新LOD组件的可见性
      if (lodComponent.getCurrentLevel() !== levelToShow) {
        lodComponent.setCurrentLevel(levelToShow);
        this.eventEmitter.emit(LODSystemEventType.LEVEL_CHANGED, entity, lodComponent, levelToShow);
      }
    }
  }

  /**
   * 确定LOD级别
   * @param lodComponent LOD组件
   * @param distance 与相机的距离
   * @returns LOD级别
   */
  private determineLODLevel(lodComponent: LODComponent, distance: number): LODLevel {
    const levels = lodComponent.getLevels();

    // 如果没有级别，则返回null
    if (levels.length === 0) {
      return null;
    }

    // 如果只有一个级别，则返回该级别
    if (levels.length === 1) {
      return levels[0].level;
    }

    // 根据距离确定级别
    for (let i = 0; i < levels.length; i++) {
      const level = levels[i];
      if (distance < level.distance) {
        return level.level;
      }
    }

    // 如果距离大于所有级别的距离，则返回最后一个级别
    return levels[levels.length - 1].level;
  }

  /**
   * 手动更新LOD
   */
  public manualUpdate(): void {
    if (!this.isEnabled()) {
      return;
    }

    this.updateLODComponents();
  }

  /**
   * 设置是否启用
   * @param enabled 是否启用
   */
  public setEnabled(enabled: boolean): void {
    super.setEnabled(enabled);
  }

  /**
   * 设置是否自动更新
   * @param autoUpdate 是否自动更新
   */
  public setAutoUpdate(autoUpdate: boolean): void {
    this.autoUpdate = autoUpdate;
  }

  /**
   * 获取是否自动更新
   * @returns 是否自动更新
   */
  public isAutoUpdate(): boolean {
    return this.autoUpdate;
  }

  /**
   * 设置更新频率
   * @param frequency 更新频率
   */
  public setUpdateFrequency(frequency: number): void {
    this.updateFrequency = frequency;
  }

  /**
   * 获取更新频率
   * @returns 更新频率
   */
  public getUpdateFrequency(): number {
    return this.updateFrequency;
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public addEventListener(type: LODSystemEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(type, listener);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public removeEventListener(type: LODSystemEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(type, listener);
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    this.lodComponents.clear();
    this.eventEmitter.removeAllListeners();
  }

  /**
   * 自动生成LOD
   * @param entity 实体
   * @param mesh 网格
   * @param distances 距离配置
   * @returns 是否成功生成
   */
  public autoGenerateLOD(entity: Entity, mesh: THREE.Mesh, distances: number[] = [10, 30, 60, 100]): boolean {
    if (!this.lodGenerator) {
      this.lodGenerator = new LODGenerator();
    }

    try {
      // 生成LOD
      const result = this.lodGenerator.generate(mesh);

      // 创建LOD组件
      const lodComponent = new LODComponent();

      // 添加LOD级别
      lodComponent.addLevel({
        level: LODLevel.HIGH,
        distance: distances[0],
        mesh: result.high,
        visible: true
      });

      lodComponent.addLevel({
        level: LODLevel.MEDIUM,
        distance: distances[1],
        mesh: result.medium,
        visible: false
      });

      lodComponent.addLevel({
        level: LODLevel.LOW,
        distance: distances[2],
        mesh: result.low,
        visible: false
      });

      lodComponent.addLevel({
        level: LODLevel.VERY_LOW,
        distance: distances[3],
        mesh: result.veryLow,
        visible: false
      });

      // 添加LOD组件到实体
      entity.addComponent(lodComponent);

      // 注册LOD组件
      this.registerLODComponent(entity, lodComponent);

      return true;
    } catch (error) {
      Debug.error('自动生成LOD失败:', error);
      return false;
    }
  }

  /**
   * 自动生成LOD（自定义配置）
   * @param entity 实体
   * @param mesh 网格
   * @param levels LOD级别配置
   * @returns 是否成功生成
   */
  public autoGenerateCustomLOD(entity: Entity, mesh: THREE.Mesh, levels: { level: LODLevel; distance: number; ratio: number }[]): boolean {
    if (!this.lodGenerator) {
      this.lodGenerator = new LODGenerator();
    }

    try {
      // 创建LOD组件
      const lodComponent = new LODComponent();

      // 生成并添加每个LOD级别
      for (const levelConfig of levels) {
        const lodMesh = this.lodGenerator.generateLevel(mesh, levelConfig.ratio);

        lodComponent.addLevel({
          level: levelConfig.level,
          distance: levelConfig.distance,
          mesh: lodMesh,
          visible: levelConfig.level === LODLevel.HIGH // 只有最高级别默认可见
        });
      }

      // 添加LOD组件到实体
      entity.addComponent(lodComponent);

      // 注册LOD组件
      this.registerLODComponent(entity, lodComponent);

      return true;
    } catch (error) {
      Debug.error('自动生成自定义LOD失败:', error);
      return false;
    }
  }

  /**
   * 设置是否使用自动LOD生成
   * @param useAutoLODGeneration 是否使用自动LOD生成
   */
  public setUseAutoLODGeneration(useAutoLODGeneration: boolean): void {
    this.useAutoLODGeneration = useAutoLODGeneration;

    if (this.useAutoLODGeneration && !this.lodGenerator) {
      this.lodGenerator = new LODGenerator();
    }
  }

  /**
   * 获取是否使用自动LOD生成
   * @returns 是否使用自动LOD生成
   */
  public isUseAutoLODGeneration(): boolean {
    return this.useAutoLODGeneration;
  }

  /**
   * 获取LOD生成器
   * @returns LOD生成器
   */
  public getLODGenerator(): LODGenerator | null {
    return this.lodGenerator;
  }

  /**
   * 设置LOD生成器
   * @param generator LOD生成器
   */
  public setLODGenerator(generator: LODGenerator): void {
    this.lodGenerator = generator;
  }
}
