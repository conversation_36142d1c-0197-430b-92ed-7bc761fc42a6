/**
 * UI3DComponent.ts
 *
 * 3D UI元素组件，用于创建和管理3D空间中的界面元素
 */

import type { Entity } from '../../core/Entity';
import { Vector2, Vector3, Mesh, PlaneGeometry, MeshBasicMaterial, CanvasTexture, Group, Texture, Object3D, Quaternion, Euler } from 'three';
import { UIComponent, UIComponentProps, UIComponentType } from './UIComponent';

/**
 * 3D UI元素属性
 */
export interface UI3DComponentProps extends UIComponentProps {
  // 3D特有属性
  mesh?: Mesh;
  texture?: Texture;
  canvas?: HTMLCanvasElement;
  group?: Group;

  // 3D变换属性
  rotation?: Vector3 | Euler;
  scale?: Vector3;
  lookAt?: Vector3;
  billboardMode?: BillboardMode;

  // 材质属性
  transparent?: boolean;
  opacity?: number;
  color?: string;
  emissive?: string;
  emissiveIntensity?: number;

  // 内容属性
  textContent?: string;
  fontSize?: number;
  fontFamily?: string;
  fontColor?: string;
  textAlign?: 'left' | 'center' | 'right';
  textBaseline?: 'top' | 'middle' | 'bottom';

  // 交互属性
  interactionDistance?: number;
  hoverColor?: string;
  activeColor?: string;
}

/**
 * 广告牌模式枚举
 */
export enum BillboardMode {
  NONE = 'none',
  FULL = 'full',
  Y_AXIS = 'y-axis'
}

/**
 * 3D UI元素组件
 * 用于创建和管理3D空间中的界面元素
 */
export class UI3DComponent extends UIComponent {
  // 3D对象
  mesh?: Mesh;
  texture?: Texture;
  canvas?: HTMLCanvasElement;
  context?: CanvasRenderingContext2D;
  group?: Group;

  // 3D变换
  rotation: Vector3 | Euler;
  scale: Vector3;
  lookAt?: Vector3;
  billboardMode: BillboardMode = BillboardMode.NONE;

  // 材质属性
  transparent: boolean = true;
  color?: string;
  emissive?: string;
  emissiveIntensity: number = 1.0;

  // 内容属性
  textContent: string = '';
  fontSize: number = 24;
  fontFamily: string = 'Arial';
  fontColor: string = '#ffffff';
  textAlign: 'left' | 'center' | 'right' = 'center';
  textBaseline: 'top' | 'middle' | 'bottom' = 'middle';

  // 交互属性
  interactionDistance: number = 10;
  hoverColor?: string;
  activeColor?: string;

  // 状态
  isHovered: boolean = false;
  isActive: boolean = false;
  needsUpdate: boolean = true;

  /**
   * 构造函数
   * @param entity 关联的实体
   * @param props 3D UI元素属性
   */
  constructor(entity: Entity, props: UI3DComponentProps = {}) {
    super(entity, { ...props, is3D: true });

    // 设置3D对象
    this.mesh = props.mesh;
    this.texture = props.texture;
    this.canvas = props.canvas;
    this.group = props.group || new Group();

    // 设置3D变换
    this.rotation = props.rotation || new Vector3(0, 0, 0);
    this.scale = props.scale || new Vector3(1, 1, 1);
    this.lookAt = props.lookAt;
    this.billboardMode = props.billboardMode || BillboardMode.NONE;

    // 设置材质属性
    this.transparent = props.transparent !== undefined ? props.transparent : true;
    this.color = props.color;
    this.emissive = props.emissive;
    this.emissiveIntensity = props.emissiveIntensity || 1.0;

    // 设置内容属性
    this.textContent = props.textContent || '';
    this.fontSize = props.fontSize || 24;
    this.fontFamily = props.fontFamily || 'Arial';
    this.fontColor = props.fontColor || '#ffffff';
    this.textAlign = props.textAlign || 'center';
    this.textBaseline = props.textBaseline || 'middle';

    // 设置交互属性
    this.interactionDistance = props.interactionDistance || 10;
    this.hoverColor = props.hoverColor;
    this.activeColor = props.activeColor;

    // 创建3D对象（如果没有提供）
    if (!this.mesh) {
      this.createMesh();
    }

    // 将网格添加到组
    if (this.mesh && this.group && !this.group.children.includes(this.mesh)) {
      this.group.add(this.mesh);
    }

    // 设置组的位置
    if (this.group) {
      if (this.position instanceof Vector3) {
        this.group.position.copy(this.position);
      } else {
        (this.group as any).setPosition(this.position.x, this.position.y, 0);
      }

      // 设置组的旋转
      if (this.rotation instanceof Euler) {
        this.group.rotation.copy(this.rotation);
      } else {
        (this.group as any).setRotationQuaternion(this.rotation.x, this.rotation.y, this.rotation.z);
      }

      // 设置组的缩放
      this.group.scale.copy(this.scale);
    }

    // 初始渲染
    this.render();
  }

  /**
   * 创建网格
   */
  private createMesh(): void {
    // 创建画布（如果没有提供）
    if (!this.canvas) {
      this.canvas = document.createElement('canvas');
      this.canvas.width = this.size.x;
      this.canvas.height = this.size.y;
      this.context = this.canvas.getContext('2d')!;
    }

    // 创建纹理（如果没有提供）
    if (!this.texture && this.canvas) {
      this.texture = new CanvasTexture(this.canvas);
      this.texture.needsUpdate = true;
    }

    // 创建材质
    const material = new MeshBasicMaterial({
      map: this.texture,
      transparent: this.transparent,
      opacity: this.opacity
    });

    if (this.color) {
      material.color.set(this.color);
    }

    if (this.emissive) {
      (material as any).emissive = this.emissive;
      (material as any).emissiveIntensity = this.emissiveIntensity;
    }

    // 创建几何体
    const geometry = new PlaneGeometry(this.size.x / 100, this.size.y / 100);

    // 创建网格
    this.mesh = new Mesh(geometry, material);
    this.mesh.name = this.id;
  }

  /**
   * 更新画布内容
   */
  private updateCanvas(): void {
    if (!this.canvas || !this.context) return;

    // 清除画布
    this.context.clearRect(0, 0, this.canvas.width, this.canvas.height);

    // 设置背景
    if (this.backgroundColor) {
      this.context.fillStyle = this.backgroundColor;
      this.context.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }

    // 绘制边框
    if (this.borderColor && this.borderWidth > 0) {
      this.context.strokeStyle = this.borderColor;
      this.context.lineWidth = this.borderWidth;
      this.context.strokeRect(
        this.borderWidth / 2,
        this.borderWidth / 2,
        this.canvas.width - this.borderWidth,
        this.canvas.height - this.borderWidth
      );
    }

    // 绘制文本
    if (this.textContent) {
      this.context.fillStyle = this.fontColor;
      this.context.font = `${this.fontSize}px ${this.fontFamily}`;
      this.context.textAlign = this.textAlign;
      this.context.textBaseline = this.textBaseline;

      let x = this.canvas.width / 2;
      if (this.textAlign === 'left') x = this.padding.left;
      else if (this.textAlign === 'right') x = this.canvas.width - this.padding.right;

      let y = this.canvas.height / 2;
      if (this.textBaseline === 'top') y = this.padding.top;
      else if (this.textBaseline === 'bottom') y = this.canvas.height - this.padding.bottom;

      this.context.fillText(this.textContent, x, y);
    }

    // 更新纹理
    if (this.texture) {
      this.texture.needsUpdate = true;
    }
  }

  /**
   * 更新UI元素
   * @param deltaTime 时间增量
   * @param camera 相机对象（用于广告牌模式）
   */
  update(deltaTime: number, camera?: Object3D): void {
    super.update(deltaTime);

    // 更新广告牌模式
    if (camera && this.billboardMode !== BillboardMode.NONE && this.group) {
      if (this.billboardMode === BillboardMode.FULL) {
        // 完全面向相机
        this.group.quaternion.copy(camera.quaternion);
      } else if (this.billboardMode === BillboardMode.Y_AXIS) {
        // 仅在Y轴上面向相机
        const cameraPosition = new Vector3();
        camera.getWorldPosition(cameraPosition);

        const groupPosition = new Vector3();
        this.group.getWorldPosition(groupPosition);

        cameraPosition.y = groupPosition.y;
        this.group.lookAt(cameraPosition);
      }
    }

    // 更新lookAt目标
    if (this.lookAt && this.group) {
      this.group.lookAt(this.lookAt);
    }

    // 如果需要更新，则更新画布
    if (this.needsUpdate) {
      this.updateCanvas();
      this.needsUpdate = false;
    }
  }

  /**
   * 渲染UI元素
   */
  override render(): void {
    super.render();

    // 更新画布内容
    this.updateCanvas();
  }

  /**
   * 销毁UI元素
   */
  override dispose(): void {
    super.dispose();

    // 销毁网格
    if (this.mesh) {
      (this.mesh.geometry as any).dispose();
      (this.mesh.material as MeshBasicMaterial).dispose();
      if (this.mesh.parent) {
        this.mesh.parent.remove(this.mesh);
      }
    }

    // 销毁纹理
    if (this.texture) {
      (this.texture as any).dispose();
    }

    // 销毁组
    if (this.group) {
      while (this.group.children.length > 0) {
        const child = this.group.children[0];
        this.group.remove(child);
      }
    }

    this.mesh = undefined;
    this.texture = undefined;
    this.canvas = undefined;
    this.context = undefined;
    this.group = undefined;
  }

  /**
   * 设置文本内容
   * @param text 文本内容
   */
  setText(text: string): void {
    this.textContent = text;
    this.needsUpdate = true;
  }

  /**
   * 设置字体大小
   * @param size 字体大小
   */
  setFontSize(size: number): void {
    this.fontSize = size;
    this.needsUpdate = true;
  }

  /**
   * 设置字体颜色
   * @param color 字体颜色
   */
  setFontColor(color: string): void {
    this.fontColor = color;
    this.needsUpdate = true;
  }

  /**
   * 设置悬停状态
   * @param hovered 是否悬停
   */
  setHovered(hovered: boolean): void {
    if (this.isHovered === hovered) return;

    this.isHovered = hovered;

    // 更新材质颜色
    if (this.mesh && this.hoverColor) {
      const material = this.mesh.material as MeshBasicMaterial;
      if (hovered && !this.isActive) {
        material.color.set(this.hoverColor);
      } else if (!this.isActive) {
        material.color.set(this.color || '#ffffff');
      }
    }

    this.needsUpdate = true;
  }

  /**
   * 设置激活状态
   * @param active 是否激活
   */
  setActive(active: boolean): void {
    if (this.isActive === active) return;

    this.isActive = active;

    // 更新材质颜色
    if (this.mesh && this.activeColor) {
      const material = this.mesh.material as MeshBasicMaterial;
      if (active) {
        material.color.set(this.activeColor);
      } else if (this.isHovered && this.hoverColor) {
        material.color.set(this.hoverColor);
      } else {
        material.color.set(this.color || '#ffffff');
      }
    }

    this.needsUpdate = true;
  }

  /**
   * 获取3D对象
   * @returns 3D对象（Group）
   */
  getObject3D(): Object3D | undefined {
    return this.group;
  }

  /**
   * 设置位置
   * @param position 新位置
   */
  override setPosition(position: Vector3 | Vector2): void {
    super.setPosition(position);

    if (this.group) {
      if (position instanceof Vector3) {
        this.group.position.copy(position);
      } else {
        (this.group as any).setPosition(position.x, position.y, 0);
      }
    }
  }

  /**
   * 设置旋转
   * @param rotation 新旋转
   */
  setRotation(rotation: Vector3 | Euler): void {
    this.rotation = rotation;

    if (this.group) {
      if (rotation instanceof Euler) {
        this.group.rotation.copy(rotation);
      } else {
        (this.group as any).setRotationQuaternion(rotation.x, rotation.y, rotation.z);
      }
    }
  }

  /**
   * 设置缩放
   * @param scale 新缩放
   */
  setScale(scale: Vector3): void {
    this.scale = scale;

    if (this.group) {
      this.group.scale.copy(scale);
    }
  }
}
