/**
 * 水体组件
 * 用于表示水体及其物理属性
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { Debug } from '../../utils/Debug';

/**
 * 水体类型
 */
export enum WaterBodyType {
  /** 湖泊 */
  LAKE = 'lake',
  /** 河流 */
  RIVER = 'river',
  /** 海洋 */
  OCEAN = 'ocean',
  /** 瀑布 */
  WATERFALL = 'waterfall',
  /** 温泉 */
  HOT_SPRING = 'hot_spring',
  /** 地下湖泊 */
  UNDERGROUND_LAKE = 'underground_lake',
  /** 地下河流 */
  UNDERGROUND_RIVER = 'underground_river',
  /** 喷泉 */
  FOUNTAIN = 'fountain',
  /** 雨水 */
  RAIN_WATER = 'rain_water'
}

/**
 * 水体形状
 */
export enum WaterBodyShape {
  /** 平面 */
  PLANE = 'plane',
  /** 立方体 */
  BOX = 'box',
  /** 球体 */
  SPHERE = 'sphere',
  /** 圆柱体 */
  CYLINDER = 'cylinder',
  /** 自定义 */
  CUSTOM = 'custom'
}

/**
 * 水流方向
 */
export interface WaterFlowDirection {
  /** X方向流速 */
  x: number;
  /** Y方向流速 */
  y: number;
  /** Z方向流速 */
  z: number;
}

/**
 * 水体波动参数
 */
export interface WaterWaveParams {
  /** 波动幅度 */
  amplitude: number;
  /** 波动频率 */
  frequency: number;
  /** 波动速度 */
  speed: number;
  /** 波动方向 */
  direction: { x: number, z: number };
}

/**
 * 水体组件配置
 */
export interface WaterBodyConfig {
  /** 水体类型 */
  type?: WaterBodyType;
  /** 水体形状 */
  shape?: WaterBodyShape;
  /** 水体尺寸 */
  size?: { width: number, height: number, depth: number };
  /** 水体密度 */
  density?: number;
  /** 水体粘度 */
  viscosity?: number;
  /** 水体温度 */
  temperature?: number;
  /** 水体流向 */
  flowDirection?: WaterFlowDirection;
  /** 水体流速 */
  flowSpeed?: number;
  /** 水体波动参数 */
  waveParams?: WaterWaveParams;
  /** 是否启用浮力 */
  enableBuoyancy?: boolean;
  /** 是否启用阻力 */
  enableDrag?: boolean;
  /** 是否启用波动 */
  enableWaves?: boolean;
  /** 是否启用流动 */
  enableFlow?: boolean;
  /** 是否启用反射 */
  enableReflection?: boolean;
  /** 是否启用折射 */
  enableRefraction?: boolean;
  /** 是否启用因果波纹 */
  enableCaustics?: boolean;
  /** 是否启用水下雾效 */
  enableUnderwaterFog?: boolean;
  /** 是否启用水下扭曲 */
  enableUnderwaterDistortion?: boolean;
  /** 是否启用水体粒子 */
  enableParticles?: boolean;
  /** 是否启用 */
  enabled?: boolean;
}

/**
 * 水体组件
 */
export class WaterBodyComponent extends Component {
  /** 水体类型 */
  private waterType: WaterBodyType;
  /** 水体形状 */
  private shape: WaterBodyShape;
  /** 水体尺寸 */
  private size: { width: number, height: number, depth: number };
  /** 水体密度 */
  private density: number;
  /** 水体粘度 */
  private viscosity: number;
  /** 水体温度 */
  private temperature: number;
  /** 水体流向 */
  private flowDirection: WaterFlowDirection;
  /** 水体流速 */
  private flowSpeed: number;
  /** 水体波动参数 */
  private waveParams: WaterWaveParams;
  /** 是否启用浮力 */
  private enableBuoyancy: boolean;
  /** 是否启用阻力 */
  private enableDrag: boolean;
  /** 是否启用波动 */
  private enableWaves: boolean;
  /** 是否启用流动 */
  private enableFlow: boolean;
  /** 是否启用反射 */
  private enableReflection: boolean;
  /** 是否启用折射 */
  private enableRefraction: boolean;
  /** 是否启用因果波纹 */
  private enableCaustics: boolean;
  /** 是否启用水下雾效 */
  private enableUnderwaterFog: boolean;
  /** 是否启用水下扭曲 */
  private enableUnderwaterDistortion: boolean;
  /** 是否启用水体粒子 */
  private enableParticles: boolean;
  /** 是否已初始化 */
  private initialized: boolean = false;
  /** 水体网格 */
  private waterMesh: THREE.Mesh | null = null;
  /** 水体材质 */
  private waterMaterial: THREE.Material | null = null;
  /** 水面高度图 */
  private heightMap: Float32Array | null = null;
  /** 水面法线图 */
  private normalMap: Float32Array | null = null;
  /** 水流速度图 */
  private velocityMap: Float32Array | null = null;
  /** 水体粒子系统 */
  private particleSystem: any = null;
  /** 波动时间 */
  private waveTime: number = 0;
  /** 分裂效果列表 */
  private splittingEffects: any[] = [];

  /**
   * 构造函数
   * @param entity 实体
   * @param config 配置
   */
  constructor(entity: Entity, config: WaterBodyConfig = {}) {
    super('WaterBodyComponent');
    this.setEntity(entity);

    // 设置默认值
    this.waterType = config.type || WaterBodyType.LAKE;
    this.shape = config.shape || WaterBodyShape.PLANE;
    this.size = config.size || { width: 10, height: 1, depth: 10 };
    this.density = config.density || 1000; // 水的密度约为1000 kg/m³
    this.viscosity = config.viscosity || 1.0;
    this.temperature = config.temperature || 20; // 摄氏度
    this.flowDirection = config.flowDirection || { x: 0, y: 0, z: 0 };
    this.flowSpeed = config.flowSpeed || 0;
    this.waveParams = config.waveParams || { amplitude: 0.1, frequency: 1, speed: 1, direction: { x: 1, z: 0 } };
    this.enableBuoyancy = config.enableBuoyancy !== undefined ? config.enableBuoyancy : true;
    this.enableDrag = config.enableDrag !== undefined ? config.enableDrag : true;
    this.enableWaves = config.enableWaves !== undefined ? config.enableWaves : true;
    this.enableFlow = config.enableFlow !== undefined ? config.enableFlow : false;
    this.enableReflection = config.enableReflection !== undefined ? config.enableReflection : true;
    this.enableRefraction = config.enableRefraction !== undefined ? config.enableRefraction : true;
    this.enableCaustics = config.enableCaustics !== undefined ? config.enableCaustics : false;
    this.enableUnderwaterFog = config.enableUnderwaterFog !== undefined ? config.enableUnderwaterFog : true;
    this.enableUnderwaterDistortion = config.enableUnderwaterDistortion !== undefined ? config.enableUnderwaterDistortion : true;
    this.enableParticles = config.enableParticles !== undefined ? config.enableParticles : false;
    this.setEnabled(config.enabled !== undefined ? config.enabled : true);
  }

  /**
   * 初始化水体组件
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    // 创建水体网格
    this.createWaterMesh();

    // 创建水体材质
    this.createWaterMaterial();

    // 初始化水面高度图
    this.initializeHeightMap();

    // 初始化水面法线图
    this.initializeNormalMap();

    // 初始化水流速度图
    this.initializeVelocityMap();

    // 如果启用粒子，初始化粒子系统
    if (this.enableParticles) {
      this.initializeParticleSystem();
    }

    this.initialized = true;
    Debug.log('WaterBodyComponent', '水体组件初始化完成');
  }

  /**
   * 创建水体网格
   */
  private createWaterMesh(): void {
    // 根据水体形状创建不同的几何体
    let geometry: THREE.BufferGeometry;

    switch (this.shape) {
      case WaterBodyShape.PLANE:
        geometry = this.createPlaneGeometry();
        break;

      case WaterBodyShape.BOX:
        geometry = this.createBoxGeometry();
        break;

      case WaterBodyShape.SPHERE:
        geometry = this.createSphereGeometry();
        break;

      case WaterBodyShape.CYLINDER:
        geometry = this.createCylinderGeometry();
        break;

      case WaterBodyShape.CUSTOM:
        geometry = this.createCustomGeometry();
        break;

      default:
        geometry = this.createPlaneGeometry();
        break;
    }

    // 创建材质
    const material = new THREE.MeshStandardMaterial({
      color: 0x0055ff,
      transparent: true,
      opacity: 0.8,
      side: THREE.DoubleSide
    });

    // 创建网格
    this.waterMesh = new THREE.Mesh(geometry, material);

    // 添加到实体
    const transform = this.entity.getComponent('Transform') as any as any as any;
    if (transform) {
      transform.add(this.waterMesh);
    }
  }

  /**
   * 创建平面几何体
   * @returns 平面几何体
   */
  private createPlaneGeometry(): THREE.BufferGeometry {
    const resolution = this.getResolution();
    return new THREE.PlaneGeometry(
      this.size.width,
      this.size.depth,
      resolution - 1,
      resolution - 1
    );
  }

  /**
   * 创建盒体几何体
   * @returns 盒体几何体
   */
  private createBoxGeometry(): THREE.BufferGeometry {
    return new THREE.BoxGeometry(
      this.size.width,
      this.size.height,
      this.size.depth
    );
  }

  /**
   * 创建球体几何体
   * @returns 球体几何体
   */
  private createSphereGeometry(): THREE.BufferGeometry {
    const radius = Math.min(this.size.width, this.size.height, this.size.depth) / 2;
    return new THREE.SphereGeometry(radius, 32, 32);
  }

  /**
   * 创建圆柱体几何体
   * @returns 圆柱体几何体
   */
  private createCylinderGeometry(): THREE.BufferGeometry {
    const radius = Math.min(this.size.width, this.size.depth) / 2;
    return new THREE.CylinderGeometry(radius, radius, this.size.height, 32);
  }

  /**
   * 创建自定义几何体
   * @returns 自定义几何体
   */
  private createCustomGeometry(): THREE.BufferGeometry {
    // 默认返回平面几何体
    return this.createPlaneGeometry();
  }

  /**
   * 创建水体材质
   */
  private createWaterMaterial(): void {
    // 创建水体材质
    this.waterMaterial = new THREE.MeshStandardMaterial({
      color: 0x0055ff,
      transparent: true,
      opacity: 0.8,
      side: THREE.DoubleSide
    });

    // 设置水体网格的材质
    if (this.waterMesh) {
      this.waterMesh.material = this.waterMaterial;
    }
  }

  /**
   * 初始化水面高度图
   */
  private initializeHeightMap(): void {
    const resolution = this.getResolution();
    this.heightMap = new Float32Array(resolution * resolution);

    // 初始化高度图为0
    for (let i = 0; i < this.heightMap.length; i++) {
      this.heightMap[i] = 0;
    }
  }

  /**
   * 初始化水面法线图
   */
  private initializeNormalMap(): void {
    const resolution = this.getResolution();
    this.normalMap = new Float32Array(resolution * resolution * 3);

    // 初始化法线图为(0,1,0)
    for (let i = 0; i < resolution * resolution; i++) {
      const index = i * 3;
      this.normalMap[index] = 0;     // x
      this.normalMap[index + 1] = 1; // y
      this.normalMap[index + 2] = 0; // z
    }
  }

  /**
   * 初始化水流速度图
   */
  private initializeVelocityMap(): void {
    const resolution = this.getResolution();
    this.velocityMap = new Float32Array(resolution * resolution * 2);

    // 初始化速度图为(0,0)
    for (let i = 0; i < resolution * resolution; i++) {
      const index = i * 2;
      this.velocityMap[index] = 0;     // x
      this.velocityMap[index + 1] = 0; // z
    }
  }

  /**
   * 初始化粒子系统
   */
  private initializeParticleSystem(): void {
    // 创建粒子系统
    this.particleSystem = {
      particles: [],
      maxParticles: 1000,

      /**
       * 创建水粒子
       * @param position 位置
       * @param velocity 速度
       * @param size 大小
       * @param lifetime 生命周期
       */
      createWaterParticle: (position, velocity, size, lifetime) => {
        // 检查是否达到最大粒子数量
        if (this.particleSystem.particles.length >= this.particleSystem.maxParticles) {
          // 查找最旧的粒子并替换
          let oldestIndex = 0;
          let oldestTime = Infinity;

          for (let i = 0; i < this.particleSystem.particles.length; i++) {
            const particle = this.particleSystem.particles[i];
            if (particle.creationTime < oldestTime) {
              oldestTime = particle.creationTime;
              oldestIndex = i;
            }
          }

          // 替换最旧的粒子
          this.particleSystem.particles[oldestIndex] = {
            position: position.clone(),
            velocity: velocity.clone(),
            size: size,
            lifetime: lifetime,
            age: 0,
            creationTime: performance.now()
          };
        } else {
          // 创建新粒子
          this.particleSystem.particles.push({
            position: position.clone(),
            velocity: velocity.clone(),
            size: size,
            lifetime: lifetime,
            age: 0,
            creationTime: performance.now()
          });
        }
      },

      /**
       * 更新粒子系统
       * @param deltaTime 时间增量
       */
      update: (deltaTime) => {
        // 更新所有粒子
        for (let i = this.particleSystem.particles.length - 1; i >= 0; i--) {
          const particle = this.particleSystem.particles[i];

          // 更新粒子年龄
          particle.age += deltaTime;

          // 如果粒子超过生命周期，移除
          if (particle.age >= particle.lifetime) {
            this.particleSystem.particles.splice(i, 1);
            continue;
          }

          // 应用重力
          particle.velocity.y -= 9.8 * deltaTime;

          // 更新位置
          particle.position.x += particle.velocity.x * deltaTime;
          particle.position.y += particle.velocity.y * deltaTime;
          particle.position.z += particle.velocity.z * deltaTime;

          // 检查是否落回水面
          const waterPosition = this.getPosition();
          if (particle.position.y <= waterPosition.y) {
            // 创建小波纹
            this.addRipple(
              particle.position,
              particle.size * 2,
              0.05
            );

            // 移除粒子
            this.particleSystem.particles.splice(i, 1);
          }
        }
      }
    };
  }

  // 其他方法将在后续实现

  /**
   * 是否已初始化
   * @returns 是否已初始化
   */
  public isInitialized(): boolean {
    return this.initialized;
  }



  /**
   * 获取水体类型
   * @returns 水体类型
   */
  public getWaterType(): WaterBodyType {
    return this.waterType;
  }

  /**
   * 获取水体形状
   * @returns 水体形状
   */
  public getShape(): WaterBodyShape {
    return this.shape;
  }

  /**
   * 获取水体尺寸
   * @returns 水体尺寸
   */
  public getSize(): { width: number, height: number, depth: number } {
    return this.size;
  }

  /**
   * 获取水体密度
   * @returns 水体密度
   */
  public getDensity(): number {
    return this.density;
  }

  /**
   * 获取水体粘度
   * @returns 水体粘度
   */
  public getViscosity(): number {
    return this.viscosity;
  }

  /**
   * 获取水体温度
   * @returns 水体温度
   */
  public getTemperature(): number {
    return this.temperature;
  }

  /**
   * 获取水体流向
   * @returns 水体流向
   */
  public getFlowDirection(): WaterFlowDirection {
    return this.flowDirection;
  }

  /**
   * 获取水体流速
   * @returns 水体流速
   */
  public getFlowSpeed(): number {
    return this.flowSpeed;
  }

  /**
   * 获取水体波动参数
   * @returns 水体波动参数
   */
  public getWaveParams(): WaterWaveParams {
    return this.waveParams;
  }

  /**
   * 是否启用浮力
   * @returns 是否启用浮力
   */
  public isBuoyancyEnabled(): boolean {
    return this.enableBuoyancy;
  }

  /**
   * 是否启用阻力
   * @returns 是否启用阻力
   */
  public isDragEnabled(): boolean {
    return this.enableDrag;
  }

  /**
   * 获取水体网格
   * @returns 水体网格
   */
  public getWaterMesh(): THREE.Mesh | null {
    return this.waterMesh;
  }

  /**
   * 获取水体材质
   * @returns 水体材质
   */
  public getWaterMaterial(): THREE.Material | null {
    return this.waterMaterial;
  }

  /**
   * 获取水面高度图
   * @returns 水面高度图
   */
  public getHeightMap(): Float32Array | null {
    return this.heightMap;
  }

  /**
   * 设置水面高度图
   * @param heightMap 水面高度图
   */
  public setHeightMap(heightMap: Float32Array): void {
    this.heightMap = heightMap;
  }

  /**
   * 获取水面法线图
   * @returns 水面法线图
   */
  public getNormalMap(): Float32Array | null {
    return this.normalMap;
  }

  /**
   * 设置水面法线图
   * @param normalMap 水面法线图
   */
  public setNormalMap(normalMap: Float32Array): void {
    this.normalMap = normalMap;
  }

  /**
   * 获取水流速度图
   * @returns 水流速度图
   */
  public getVelocityMap(): Float32Array | null {
    return this.velocityMap;
  }

  /**
   * 设置水流速度图
   * @param velocityMap 水流速度图
   */
  public setVelocityMap(velocityMap: Float32Array): void {
    this.velocityMap = velocityMap;
  }

  /**
   * 获取粒子系统
   * @returns 粒子系统
   */
  public getParticleSystem(): any {
    return this.particleSystem;
  }

  /**
   * 设置水体尺寸
   * @param size 水体尺寸
   */
  public setSize(size: { width: number, height: number, depth: number }): void {
    this.size = { ...size };

    // 重新创建水体网格
    this.createWaterMesh();
  }

  /**
   * 设置水体位置
   * @param position 水体位置
   */
  public setPosition(position: THREE.Vector3): void {
    // 获取实体的变换组件
    const transform = this.entity.getComponent('Transform') as any as any;
    if (transform) {
      transform.setPosition(position);
    }
  }

  /**
   * 设置水体旋转
   * @param rotation 水体旋转
   */
  public setRotation(rotation: THREE.Euler): void {
    // 获取实体的变换组件
    const transform = this.entity.getComponent('Transform') as any as any;
    if (transform) {
      transform.setRotation(rotation);
    }
  }

  /**
   * 设置水体颜色
   * @param color 水体颜色
   */
  public setColor(color: THREE.Color): void {
    if (this.waterMaterial && this.waterMaterial instanceof THREE.MeshStandardMaterial) {
      this.waterMaterial.color.copy(color);
    }
  }

  /**
   * 设置水体透明度
   * @param opacity 水体透明度
   */
  public setOpacity(opacity: number): void {
    if (this.waterMaterial && this.waterMaterial instanceof THREE.MeshStandardMaterial) {
      this.waterMaterial.opacity = opacity;
      this.waterMaterial.transparent = opacity < 1.0;
    }
  }

  /**
   * 设置水体流速
   * @param speed 流速
   */
  public setFlowSpeed(speed: number): void {
    this.flowSpeed = speed;
  }

  /**
   * 设置水体流向
   * @param direction 流向（可以是 THREE.Vector3 或 WaterFlowDirection）
   */
  public setFlowDirection(direction: THREE.Vector3 | WaterFlowDirection): void {
    if (direction instanceof THREE.Vector3) {
      this.flowDirection = { x: direction.x, y: direction.y, z: direction.z };
    } else {
      this.flowDirection = { ...direction };
    }
  }

  /**
   * 获取水体位置
   * @returns 水体位置
   */
  public getPosition(): THREE.Vector3 {
    // 获取实体的变换组件
    const transform = this.entity.getComponent('Transform') as any as any as any;
    if (!transform) {
      return new THREE.Vector3();
    }

    return transform.getPosition();
  }

  // 重复的getSize方法已移除

  /**
   * 获取水体颜色
   * @returns 水体颜色
   */
  public getColor(): THREE.Color {
    if (this.waterMaterial && this.waterMaterial instanceof THREE.MeshStandardMaterial) {
      return this.waterMaterial.color.clone();
    }
    return new THREE.Color(0x0055ff);
  }

  /**
   * 获取水体透明度
   * @returns 水体透明度
   */
  public getOpacity(): number {
    if (this.waterMaterial && this.waterMaterial instanceof THREE.MeshStandardMaterial) {
      return this.waterMaterial.opacity;
    }
    return 0.8;
  }

  // 重复的getFlowSpeed方法已移除

  /**
   * 获取水体旋转
   * @returns 水体旋转
   */
  public getRotation(): THREE.Quaternion {
    // 获取实体的变换组件
    const transform = this.entity.getComponent('Transform') as any as any as any;
    if (!transform) {
      return new THREE.Quaternion();
    }

    return transform.getRotation();
  }

  /**
   * 获取水体分辨率
   * @returns 水体分辨率
   */
  public getResolution(): number {
    // 默认分辨率为64
    return 64;
  }

  /**
   * 获取波动时间
   * @returns 波动时间
   */
  public getWaveTime(): number {
    return this.waveTime || 0;
  }

  /**
   * 更新波动时间
   * @param deltaTime 时间增量
   */
  public updateWaveTime(deltaTime: number): void {
    if (!this.waveTime) {
      this.waveTime = 0;
    }

    this.waveTime += deltaTime;
  }

  /**
   * 更新水体网格
   */
  public updateWaterMesh(): void {
    if (!this.waterMesh || !this.heightMap) return;

    // 获取网格几何体
    const geometry = this.waterMesh.geometry as THREE.BufferGeometry;
    if (!geometry) return;

    // 获取顶点位置属性
    const positionAttribute = geometry.getAttribute('position') as THREE.BufferAttribute;
    if (!positionAttribute) return;

    const resolution = this.getResolution();
    const positions = positionAttribute.array as Float32Array;

    // 更新顶点位置
    for (let z = 0; z < resolution; z++) {
      for (let x = 0; x < resolution; x++) {
        const vertexIndex = (z * resolution + x) * 3;
        const heightIndex = z * resolution + x;

        // 更新Y坐标（高度）
        positions[vertexIndex + 1] = this.heightMap[heightIndex];
      }
    }

    // 标记几何体需要更新
    positionAttribute.needsUpdate = true;

    // 更新法线
    geometry.computeVertexNormals();
  }

  /**
   * 添加波纹
   * @param position 位置
   * @param radius 半径
   * @param strength 强度
   */
  public addRipple(position: THREE.Vector3, radius: number, strength: number): void {
    if (!this.heightMap) return;

    const waterPosition = this.getPosition();
    const size = this.getSize();
    const resolution = this.getResolution();

    // 计算波纹在水体中的相对位置
    const relativeX = (position.x - (waterPosition.x - size.width / 2)) / size.width;
    const relativeZ = (position.z - (waterPosition.z - size.depth / 2)) / size.depth;

    // 计算波纹半径（相对于水体尺寸）
    const relativeRadius = radius / Math.max(size.width, size.depth);

    // 更新高度图
    for (let z = 0; z < resolution; z++) {
      for (let x = 0; x < resolution; x++) {
        // 计算当前点在水体中的相对位置
        const pointX = x / resolution;
        const pointZ = z / resolution;

        // 计算当前点到波纹中心的距离
        const distance = Math.sqrt(
          Math.pow(pointX - relativeX, 2) +
          Math.pow(pointZ - relativeZ, 2)
        );

        // 如果在波纹半径内，添加波纹效果
        if (distance < relativeRadius) {
          const index = z * resolution + x;

          // 计算波纹强度（基于距离）
          const rippleStrength = strength * (1 - distance / relativeRadius);

          // 添加波纹效果
          this.heightMap[index] += rippleStrength;
        }
      }
    }
  }

  /**
   * 添加分裂效果
   * @param effect 分裂效果
   */
  public addSplittingEffect(effect: any): void {
    this.splittingEffects.push(effect);
  }

  /**
   * 获取分裂效果列表
   * @returns 分裂效果列表
   */
  public getSplittingEffects(): any[] {
    return this.splittingEffects;
  }

  /**
   * 更新分裂效果
   * @param deltaTime 时间增量
   */
  public updateSplittingEffects(deltaTime: number): void {
    // 更新所有分裂效果
    for (let i = this.splittingEffects.length - 1; i >= 0; i--) {
      const effect = this.splittingEffects[i];

      // 调用效果的更新方法
      const completed = effect.update(deltaTime);

      // 如果效果已完成，移除它
      if (completed) {
        this.splittingEffects.splice(i, 1);
      }
    }
  }
}
